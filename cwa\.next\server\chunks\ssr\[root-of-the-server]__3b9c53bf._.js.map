{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/averia_serif_libre_33e0580e.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"averia_serif_libre_33e0580e-module__hVGtea__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/averia_serif_libre_33e0580e.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22fonts.ts%22,%22import%22:%22Averia_Serif_Libre%22,%22arguments%22:[{%22weight%22:[%22300%22,%22400%22,%22700%22],%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22fallback%22:[%22Times%20New%20Roman%22,%22serif%22]}],%22variableName%22:%22averia_serif_libre%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Averia Serif Libre', Times New Roman, serif\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,kKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,kKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,kKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/constants/fonts.ts"], "sourcesContent": ["import { Averia_Serif_Libre } from \"next/font/google\";\r\n\r\nconst averia_serif_libre = Averia_Serif_Libre({\r\n  weight: [\"300\", \"400\", \"700\"],\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n  fallback: [\"Times New Roman\", \"serif\"],\r\n});\r\n\r\nexport const secondaryFont =\r\n  \" capitalize \" + averia_serif_libre.className + \" \";\r\n"], "names": [], "mappings": ";;;;;AASO,MAAM,gBACX,iBAAiB,sJAAA,CAAA,UAAkB,CAAC,SAAS,GAAG", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/app/%28auth%29/layout.tsx"], "sourcesContent": ["'use client';\r\nimport React, { ReactNode } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport Link from 'next/link';\r\nimport { secondaryFont } from '@/constants/fonts';\r\n\r\nconst Layout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-background to-background/90 flex flex-col items-center justify-center p-4\">\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n        className=\"mb-8\"\r\n      >\r\n        <Link href=\"/\">\r\n          <h1\r\n            className={`${secondaryFont} text-4xl text-center text-foreground hover:text-accent transition-colors duration-300`}\r\n          >\r\n            <PERSON><PERSON><PERSON>en Art\r\n          </h1>\r\n        </Link>\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        initial={{ opacity: 0, scale: 0.95 }}\r\n        animate={{ opacity: 1, scale: 1 }}\r\n        transition={{ duration: 0.5, delay: 0.2 }}\r\n        className=\"w-full max-w-md\"\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Layout;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,SAAS,CAAC,EAAE,QAAQ,EAA2B;IACnD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,8OAAC;wBACC,WAAW,GAAG,kHAAA,CAAA,gBAAa,CAAC,sFAAsF,CAAC;kCACpH;;;;;;;;;;;;;;;;0BAML,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAK;gBACnC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;0BAET;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}]}