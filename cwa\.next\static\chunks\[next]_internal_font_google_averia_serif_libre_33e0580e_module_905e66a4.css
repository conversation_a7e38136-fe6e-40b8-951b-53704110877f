/* [next]/internal/font/google/averia_serif_libre_33e0580e.module.css [app-client] (css) */
@font-face {
  font-family: Averia Serif Libre;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/neIVzD2ms4wxr6GvjeD0X88SHPyX2xYGCSmaxq0rVLG_bA.p.0c90044a.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Averia Serif Libre;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/neIWzD2ms4wxr6GvjeD0X88SHPyX2xYOoguP648mfg.p.c0584150.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Averia Serif Libre;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/neIVzD2ms4wxr6GvjeD0X88SHPyX2xYGGS6axq0rVLG_bA.p.e14b8472.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

.averia_serif_libre_33e0580e-module__hVGtea__className {
  font-family: Averia Serif Libre, Times New Roman, serif;
  font-style: normal;
}


/*# sourceMappingURL=%5Bnext%5D_internal_font_google_averia_serif_libre_33e0580e_module_905e66a4.css.map*/