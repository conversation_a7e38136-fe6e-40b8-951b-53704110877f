{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/sections/Footer.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Link from 'next/link';\r\nimport {\r\n  Facebook,\r\n  Instagram,\r\n  Twitter,\r\n  Youtube,\r\n  Mail,\r\n  Phone,\r\n  MapPin,\r\n} from 'lucide-react';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-gray-50 border-t border-gray-200 pt-10 pb-6\">\r\n      <div className=\"container mx-auto px-4 sm:px-6\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n          {/* Company Info */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Chinioti Wooden Art</h3>\r\n            <p className=\"text-gray-600 text-sm mb-4\">\r\n              Handcrafted wooden furniture with traditional craftsmanship and\r\n              modern designs.\r\n            </p>\r\n            <div className=\"flex space-x-4\">\r\n              <Link\r\n                href=\"#\"\r\n                className=\"text-gray-500 hover:text-accent transition-colors\"\r\n                aria-label=\"Facebook\"\r\n              >\r\n                <Facebook size={18} />\r\n              </Link>\r\n              <Link\r\n                href=\"#\"\r\n                className=\"text-gray-500 hover:text-accent transition-colors\"\r\n                aria-label=\"Instagram\"\r\n              >\r\n                <Instagram size={18} />\r\n              </Link>\r\n              <Link\r\n                href=\"#\"\r\n                className=\"text-gray-500 hover:text-accent transition-colors\"\r\n                aria-label=\"Twitter\"\r\n              >\r\n                <Twitter size={18} />\r\n              </Link>\r\n              <Link\r\n                href=\"#\"\r\n                className=\"text-gray-500 hover:text-accent transition-colors\"\r\n                aria-label=\"YouTube\"\r\n              >\r\n                <Youtube size={18} />\r\n              </Link>\r\n              <a\r\n                href=\"https://wa.me/923421401866\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"text-gray-500 hover:text-green-500 transition-colors\"\r\n                aria-label=\"WhatsApp\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  width=\"18\"\r\n                  height=\"18\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"currentColor\"\r\n                >\r\n                  <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z\" />\r\n                </svg>\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Quick Links */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Quick Links</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li>\r\n                <Link\r\n                  href=\"/products\"\r\n                  className=\"text-gray-600 hover:text-accent text-sm transition-colors\"\r\n                >\r\n                  Products\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link\r\n                  href=\"/about-us\"\r\n                  className=\"text-gray-600 hover:text-accent text-sm transition-colors\"\r\n                >\r\n                  About Us\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link\r\n                  href=\"/contact\"\r\n                  className=\"text-gray-600 hover:text-accent text-sm transition-colors\"\r\n                >\r\n                  Contact\r\n                </Link>\r\n              </li>\r\n              {/* <li>\r\n                <Link\r\n                  href=\"/video-blogs\"\r\n                  className=\"text-gray-600 hover:text-accent text-sm transition-colors\"\r\n                >\r\n                  VLOGS\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link\r\n                  href=\"/video-blogs\"\r\n                  className=\"text-gray-600 hover:text-accent text-sm transition-colors\"\r\n                >\r\n                  VLOGS\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link\r\n                  href=\"/blogs\"\r\n                  className=\"text-gray-600 hover:text-accent text-sm transition-colors\"\r\n                >\r\n                  Blog\r\n                </Link>\r\n              </li> */}\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Categories */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Categories</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li>\r\n                <Link\r\n                  href=\"/products?category=furniture\"\r\n                  className=\"text-gray-600 hover:text-accent text-sm transition-colors\"\r\n                >\r\n                  Furniture\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link\r\n                  href=\"/products?category=sofas\"\r\n                  className=\"text-gray-600 hover:text-accent text-sm transition-colors\"\r\n                >\r\n                  Sofas\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link\r\n                  href=\"/products?category=beds\"\r\n                  className=\"text-gray-600 hover:text-accent text-sm transition-colors\"\r\n                >\r\n                  Beds\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link\r\n                  href=\"/products?category=tables\"\r\n                  className=\"text-gray-600 hover:text-accent text-sm transition-colors\"\r\n                >\r\n                  Tables\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Contact Info */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Contact Us</h3>\r\n            <ul className=\"space-y-3\">\r\n              <li className=\"flex items-start gap-3\">\r\n                <MapPin\r\n                  size={18}\r\n                  className=\"text-accent flex-shrink-0 mt-0.5\"\r\n                />\r\n                <span className=\"text-gray-600 text-sm\">\r\n                  123 Furniture Street, Chiniot, Punjab, Pakistan\r\n                </span>\r\n              </li>\r\n              <li className=\"flex items-center gap-3\">\r\n                <Phone size={18} className=\"text-accent flex-shrink-0\" />\r\n                <span className=\"text-gray-600 text-sm\">+92 ************</span>\r\n              </li>\r\n              <li className=\"flex items-center gap-3\">\r\n                <Mail size={18} className=\"text-accent flex-shrink-0\" />\r\n                <span className=\"text-gray-600 text-sm\">\r\n                  <EMAIL>\r\n                </span>\r\n              </li>\r\n              <li className=\"flex items-center gap-3\">\r\n                <a\r\n                  href=\"https://wa.me/923421401866\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"flex items-center gap-3 text-gray-600 hover:text-green-500 transition-colors\"\r\n                >\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    width=\"18\"\r\n                    height=\"18\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"currentColor\"\r\n                    className=\"text-green-500 flex-shrink-0\"\r\n                  >\r\n                    <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z\" />\r\n                  </svg>\r\n                  <span className=\"text-sm\">Chat on WhatsApp</span>\r\n                </a>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-200 mt-8 pt-6\">\r\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\r\n            <p className=\"text-gray-500 text-sm mb-4 md:mb-0\">\r\n              &copy; {new Date().getFullYear()} Chinioti Wooden Art. All rights\r\n              reserved.\r\n            </p>\r\n            <div className=\"flex space-x-4\">\r\n              <Link\r\n                href=\"/privacy-policy\"\r\n                className=\"text-gray-500 hover:text-accent text-sm transition-colors\"\r\n              >\r\n                Privacy Policy\r\n              </Link>\r\n              <Link\r\n                href=\"/terms-of-service\"\r\n                className=\"text-gray-500 hover:text-accent text-sm transition-colors\"\r\n              >\r\n                Terms of Service\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAUA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAI1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;;;;;;sDAElB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;sDAEnB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;sDAEjB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;sDAEjB,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAW;sDAEX,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAgCP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAIH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;sCAQP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,0MAAA,CAAA,SAAM;oDACL,MAAM;oDACN,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAI1C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC3B,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAE1C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAI1C,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,8OAAC;wDACC,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAEV,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;kEAEV,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAqC;oCACxC,IAAI,OAAO,WAAW;oCAAG;;;;;;;0CAGnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\nimport { CurrencyCode, CURRENCY_CONFIG } from '@/constants/helpers';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n// Legacy function - kept for backward compatibility\r\nexport function formatCurrency(amount: number): string {\r\n  return new Intl.NumberFormat('en-US', {\r\n    style: 'currency',\r\n    currency: 'PKR',\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 0,\r\n  }).format(amount);\r\n}\r\n\r\n// Enhanced currency formatting function\r\nexport function formatCurrencyWithCode(\r\n  amount: number,\r\n  currency: CurrencyCode = 'USD',\r\n  convertFromUSD: boolean = true\r\n): string {\r\n  const currencyInfo = CURRENCY_CONFIG[currency];\r\n\r\n  // Convert from USD to target currency if needed\r\n  const finalAmount = convertFromUSD\r\n    ? Math.round(amount * currencyInfo.rate)\r\n    : amount;\r\n\r\n  return new Intl.NumberFormat('en-US', {\r\n    style: 'currency',\r\n    currency: currency,\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 0,\r\n  }).format(finalAmount);\r\n}\r\n\r\n// Convert price between currencies\r\nexport function convertCurrency(\r\n  amount: number,\r\n  fromCurrency: CurrencyCode = 'USD',\r\n  toCurrency: CurrencyCode = 'USD'\r\n): number {\r\n  if (fromCurrency === toCurrency) return amount;\r\n\r\n  // Convert to USD first (base currency)\r\n  const amountInUSD = amount / CURRENCY_CONFIG[fromCurrency].rate;\r\n\r\n  // Convert from USD to target currency\r\n  const convertedAmount = amountInUSD * CURRENCY_CONFIG[toCurrency].rate;\r\n\r\n  return Math.round(convertedAmount);\r\n}\r\n\r\n// Format price with both currencies for comparison\r\nexport function formatDualCurrency(amount: number): string {\r\n  const usdAmount = formatCurrencyWithCode(amount, 'USD', false);\r\n  const pkrAmount = formatCurrencyWithCode(amount, 'PKR', true);\r\n  return `${usdAmount} / ${pkrAmount}`;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,uBACd,MAAc,EACd,WAAyB,KAAK,EAC9B,iBAA0B,IAAI;IAE9B,MAAM,eAAe,oHAAA,CAAA,kBAAe,CAAC,SAAS;IAE9C,gDAAgD;IAChD,MAAM,cAAc,iBAChB,KAAK,KAAK,CAAC,SAAS,aAAa,IAAI,IACrC;IAEJ,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,gBACd,MAAc,EACd,eAA6B,KAAK,EAClC,aAA2B,KAAK;IAEhC,IAAI,iBAAiB,YAAY,OAAO;IAExC,uCAAuC;IACvC,MAAM,cAAc,SAAS,oHAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,IAAI;IAE/D,sCAAsC;IACtC,MAAM,kBAAkB,cAAc,oHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,IAAI;IAEtE,OAAO,KAAK,KAAK,CAAC;AACpB;AAGO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAY,uBAAuB,QAAQ,OAAO;IACxD,MAAM,YAAY,uBAAuB,QAAQ,OAAO;IACxD,OAAO,GAAG,UAAU,GAAG,EAAE,WAAW;AACtC", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"uppercase inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive min-h-[44px] min-w-[44px]\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-accent text-background shadow-xs hover:bg-accent/90 cursor-pointer\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent/50 hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"underline-offset-4  hover:underline hover:text-accent cursor-pointer\",\r\n      },\r\n      size: {\r\n        default: \"h-11 py-2 px-4 m-1 has-[>svg]:px-3\",\r\n        sm: \"h-10 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-12 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-11\",\r\n      },\r\n      color: {\r\n        default: \"\",\r\n        light: \"text-forground\",\r\n        dark: \"text-background\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"link\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  color,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & VariantProps<typeof buttonVariants>) {\r\n  return (\r\n    <button\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className, color }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,meACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;QACA,OAAO;YACL,SAAS;YACT,OAAO;YACP,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,KAAK,EACL,GAAG,OACkE;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;YAAW;QAAM;QAC9D,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/contexts/CartContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { Cart, CartContextType, CartItem } from '@/types/cart';\r\nimport { ProductData } from '@/types';\r\nimport { toast } from 'sonner';\r\nimport { convertCurrency } from '@/lib/utils';\r\nimport { CurrencyCode } from '@/constants/helpers';\r\n\r\n// Initial cart state\r\nconst initialCart: Cart = {\r\n  items: [],\r\n  totalItems: 0,\r\n  subtotal: 0,\r\n  discount: 0,\r\n  total: 0,\r\n};\r\n\r\n// Create context\r\nconst CartContext = createContext<CartContextType | undefined>(undefined);\r\n\r\n// Provider component\r\nexport const CartProvider = ({ children }: { children: ReactNode }) => {\r\n  const [cart, setCart] = useState<Cart>(initialCart);\r\n\r\n  // Load cart from localStorage on initial render\r\n  useEffect(() => {\r\n    const savedCart = localStorage.getItem('cart');\r\n    if (savedCart) {\r\n      try {\r\n        setCart(JSON.parse(savedCart));\r\n      } catch (error) {\r\n        console.error('Failed to parse cart from localStorage:', error);\r\n        localStorage.removeItem('cart');\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Save cart to localStorage whenever it changes\r\n  useEffect(() => {\r\n    localStorage.setItem('cart', JSON.stringify(cart));\r\n  }, [cart]);\r\n\r\n  // Calculate cart totals with currency support\r\n  const calculateTotals = (items: CartItem[], currency: CurrencyCode = 'USD'): Cart => {\r\n    const totalItems = items.reduce((total, item) => total + item.quantity, 0);\r\n\r\n    const subtotal = items.reduce(\r\n      (total, item) => {\r\n        // Convert price to selected currency\r\n        const convertedPrice = convertCurrency(item.product.price, 'USD', currency);\r\n        return total + convertedPrice * item.quantity;\r\n      },\r\n      0\r\n    );\r\n\r\n    const discount = items.reduce(\r\n      (total, item) => {\r\n        if (item.product.discount) {\r\n          const convertedPrice = convertCurrency(item.product.price, 'USD', currency);\r\n          const discountAmount = (convertedPrice * parseInt(item.product.discount) / 100) * item.quantity;\r\n          return total + discountAmount;\r\n        }\r\n        return total;\r\n      },\r\n      0\r\n    );\r\n\r\n    return {\r\n      items,\r\n      totalItems,\r\n      subtotal,\r\n      discount,\r\n      total: subtotal - discount,\r\n    };\r\n  };\r\n\r\n  // Add product to cart\r\n  const addToCart = (product: ProductData, quantity: number = 1) => {\r\n    setCart((prevCart) => {\r\n      const existingItemIndex = prevCart.items.findIndex(\r\n        (item) => item.product.id === product.id\r\n      );\r\n\r\n      let updatedItems: CartItem[];\r\n\r\n      if (existingItemIndex >= 0) {\r\n        // Update quantity if product already in cart\r\n        updatedItems = [...prevCart.items];\r\n        updatedItems[existingItemIndex] = {\r\n          ...updatedItems[existingItemIndex],\r\n          quantity: updatedItems[existingItemIndex].quantity + quantity,\r\n        };\r\n        toast.success(`Updated ${product.title} quantity in cart`);\r\n      } else {\r\n        // Add new product to cart\r\n        updatedItems = [...prevCart.items, { product, quantity }];\r\n        toast.success(`Added ${product.title} to cart`);\r\n      }\r\n\r\n      return calculateTotals(updatedItems);\r\n    });\r\n  };\r\n\r\n  // Remove product from cart\r\n  const removeFromCart = (productId: string) => {\r\n    setCart((prevCart) => {\r\n      const updatedItems = prevCart.items.filter(\r\n        (item) => item.product.id !== productId\r\n      );\r\n      \r\n      const removedItem = prevCart.items.find(item => item.product.id === productId);\r\n      if (removedItem) {\r\n        toast.info(`Removed ${removedItem.product.title} from cart`);\r\n      }\r\n      \r\n      return calculateTotals(updatedItems);\r\n    });\r\n  };\r\n\r\n  // Update product quantity\r\n  const updateQuantity = (productId: string, quantity: number) => {\r\n    if (quantity <= 0) {\r\n      removeFromCart(productId);\r\n      return;\r\n    }\r\n\r\n    setCart((prevCart) => {\r\n      const updatedItems = prevCart.items.map((item) =>\r\n        item.product.id === productId ? { ...item, quantity } : item\r\n      );\r\n      \r\n      return calculateTotals(updatedItems);\r\n    });\r\n  };\r\n\r\n  // Clear cart\r\n  const clearCart = () => {\r\n    setCart(initialCart);\r\n    toast.info('Cart cleared');\r\n  };\r\n\r\n  // Check if product is in cart\r\n  const isInCart = (productId: string): boolean => {\r\n    return cart.items.some((item) => item.product.id === productId);\r\n  };\r\n\r\n  return (\r\n    <CartContext.Provider\r\n      value={{\r\n        cart,\r\n        addToCart,\r\n        removeFromCart,\r\n        updateQuantity,\r\n        clearCart,\r\n        isInCart,\r\n      }}\r\n    >\r\n      {children}\r\n    </CartContext.Provider>\r\n  );\r\n};\r\n\r\n// Custom hook to use cart context\r\nexport const useCart = (): CartContextType => {\r\n  const context = useContext(CartContext);\r\n  if (context === undefined) {\r\n    throw new Error('useCart must be used within a CartProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AACA;AANA;;;;;AASA,qBAAqB;AACrB,MAAM,cAAoB;IACxB,OAAO,EAAE;IACT,YAAY;IACZ,UAAU;IACV,UAAU;IACV,OAAO;AACT;AAEA,iBAAiB;AACjB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAGxD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IAEvC,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,WAAW;YACb,IAAI;gBACF,QAAQ,KAAK,KAAK,CAAC;YACrB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2CAA2C;gBACzD,aAAa,UAAU,CAAC;YAC1B;QACF;IACF,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;IAC9C,GAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,MAAM,kBAAkB,CAAC,OAAmB,WAAyB,KAAK;QACxE,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;QAExE,MAAM,WAAW,MAAM,MAAM,CAC3B,CAAC,OAAO;YACN,qCAAqC;YACrC,MAAM,iBAAiB,CAAA,GAAA,4GAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,OAAO,CAAC,KAAK,EAAE,OAAO;YAClE,OAAO,QAAQ,iBAAiB,KAAK,QAAQ;QAC/C,GACA;QAGF,MAAM,WAAW,MAAM,MAAM,CAC3B,CAAC,OAAO;YACN,IAAI,KAAK,OAAO,CAAC,QAAQ,EAAE;gBACzB,MAAM,iBAAiB,CAAA,GAAA,4GAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,OAAO,CAAC,KAAK,EAAE,OAAO;gBAClE,MAAM,iBAAiB,AAAC,iBAAiB,SAAS,KAAK,OAAO,CAAC,QAAQ,IAAI,MAAO,KAAK,QAAQ;gBAC/F,OAAO,QAAQ;YACjB;YACA,OAAO;QACT,GACA;QAGF,OAAO;YACL;YACA;YACA;YACA;YACA,OAAO,WAAW;QACpB;IACF;IAEA,sBAAsB;IACtB,MAAM,YAAY,CAAC,SAAsB,WAAmB,CAAC;QAC3D,QAAQ,CAAC;YACP,MAAM,oBAAoB,SAAS,KAAK,CAAC,SAAS,CAChD,CAAC,OAAS,KAAK,OAAO,CAAC,EAAE,KAAK,QAAQ,EAAE;YAG1C,IAAI;YAEJ,IAAI,qBAAqB,GAAG;gBAC1B,6CAA6C;gBAC7C,eAAe;uBAAI,SAAS,KAAK;iBAAC;gBAClC,YAAY,CAAC,kBAAkB,GAAG;oBAChC,GAAG,YAAY,CAAC,kBAAkB;oBAClC,UAAU,YAAY,CAAC,kBAAkB,CAAC,QAAQ,GAAG;gBACvD;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,QAAQ,KAAK,CAAC,iBAAiB,CAAC;YAC3D,OAAO;gBACL,0BAA0B;gBAC1B,eAAe;uBAAI,SAAS,KAAK;oBAAE;wBAAE;wBAAS;oBAAS;iBAAE;gBACzD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,QAAQ,KAAK,CAAC,QAAQ,CAAC;YAChD;YAEA,OAAO,gBAAgB;QACzB;IACF;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,CAAC;QACtB,QAAQ,CAAC;YACP,MAAM,eAAe,SAAS,KAAK,CAAC,MAAM,CACxC,CAAC,OAAS,KAAK,OAAO,CAAC,EAAE,KAAK;YAGhC,MAAM,cAAc,SAAS,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK;YACpE,IAAI,aAAa;gBACf,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,YAAY,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC;YAC7D;YAEA,OAAO,gBAAgB;QACzB;IACF;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,CAAC,WAAmB;QACzC,IAAI,YAAY,GAAG;YACjB,eAAe;YACf;QACF;QAEA,QAAQ,CAAC;YACP,MAAM,eAAe,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,OACvC,KAAK,OAAO,CAAC,EAAE,KAAK,YAAY;oBAAE,GAAG,IAAI;oBAAE;gBAAS,IAAI;YAG1D,OAAO,gBAAgB;QACzB;IACF;IAEA,aAAa;IACb,MAAM,YAAY;QAChB,QAAQ;QACR,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,8BAA8B;IAC9B,MAAM,WAAW,CAAC;QAChB,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,OAAS,KAAK,OAAO,CAAC,EAAE,KAAK;IACvD;IAEA,qBACE,8OAAC,YAAY,QAAQ;QACnB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAGO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/cart/CartIcon.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport { HiOutlineShoppingBag } from 'react-icons/hi2';\r\nimport { motion } from 'framer-motion';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport Link from 'next/link';\r\n\r\ninterface CartIconProps {\r\n  className?: string;\r\n}\r\n\r\nconst CartIcon: React.FC<CartIconProps> = ({ className = '' }) => {\r\n  const { cart } = useCart();\r\n\r\n  const iconVariants = {\r\n    hover: {\r\n      scale: 1.2,\r\n      color: 'var(--accent)',\r\n      transition: { type: 'spring', stiffness: 400, damping: 10 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <Link href=\"/cart\" aria-label={`Shopping cart with ${cart.totalItems} items`}>\r\n      <motion.div\r\n        className={`relative cursor-pointer p-1.5 sm:p-2 hover:bg-accent/10 rounded-full transition-colors min-w-[40px] min-h-[40px] flex items-center justify-center ${className}`}\r\n        whileHover=\"hover\"\r\n        variants={iconVariants}\r\n      >\r\n        <HiOutlineShoppingBag size={18} className=\"sm:w-5 sm:h-5 md:w-[22px] md:h-[22px]\" />\r\n\r\n        {cart.totalItems > 0 && (\r\n          <motion.div\r\n            className=\"absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 bg-accent text-white text-xs font-bold rounded-full w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center shadow-sm text-[10px] sm:text-xs\"\r\n            initial={{ scale: 0 }}\r\n            animate={{ scale: 1 }}\r\n            transition={{ type: 'spring', stiffness: 500, damping: 15 }}\r\n          >\r\n            {cart.totalItems > 9 ? \"9+\" : cart.totalItems}\r\n          </motion.div>\r\n        )}\r\n      </motion.div>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default CartIcon;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWA,MAAM,WAAoC,CAAC,EAAE,YAAY,EAAE,EAAE;IAC3D,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,eAAe;QACnB,OAAO;YACL,OAAO;YACP,OAAO;YACP,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;QAC5D;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAK;QAAQ,cAAY,CAAC,mBAAmB,EAAE,KAAK,UAAU,CAAC,MAAM,CAAC;kBAC1E,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAC,kJAAkJ,EAAE,WAAW;YAC3K,YAAW;YACX,UAAU;;8BAEV,8OAAC,+IAAA,CAAA,uBAAoB;oBAAC,MAAM;oBAAI,WAAU;;;;;;gBAEzC,KAAK,UAAU,GAAG,mBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;oBAAE;oBACpB,SAAS;wBAAE,OAAO;oBAAE;oBACpB,YAAY;wBAAE,MAAM;wBAAU,WAAW;wBAAK,SAAS;oBAAG;8BAEzD,KAAK,UAAU,GAAG,IAAI,OAAO,KAAK,UAAU;;;;;;;;;;;;;;;;;AAMzD;uCAEe", "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/CurrencySelector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useCurrency } from '@/contexts/CurrencyContext';\r\nimport { CurrencyCode } from '@/constants/helpers';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { ChevronDown, DollarSign } from 'lucide-react';\r\n\r\ninterface CurrencySelectorProps {\r\n  className?: string;\r\n  variant?: 'default' | 'compact';\r\n}\r\n\r\nconst CurrencySelector: React.FC<CurrencySelectorProps> = ({ \r\n  className = '', \r\n  variant = 'default' \r\n}) => {\r\n  const { selectedCurrency, setCurrency, getAllCurrencies } = useCurrency();\r\n  const currencies = getAllCurrencies();\r\n\r\n  const handleCurrencyChange = (currency: CurrencyCode) => {\r\n    setCurrency(currency);\r\n  };\r\n\r\n  if (variant === 'compact') {\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            className={`h-9 sm:h-8 px-2 sm:px-2 text-xs min-w-[44px] flex items-center justify-center ${className}`}\r\n          >\r\n            <DollarSign size={14} className=\"sm:w-3.5 sm:h-3.5 mr-1\" />\r\n            <span className=\"hidden sm:inline text-xs\">{currencies[selectedCurrency].code}</span>\r\n            <span className=\"sm:hidden text-xs\">{currencies[selectedCurrency].symbol}</span>\r\n            <ChevronDown size={12} className=\"sm:w-3 sm:h-3 ml-1\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\" className=\"w-32 sm:w-36 space-y-1 p-1 z-[60]\">\r\n          {Object.entries(currencies).map(([code, currency]) => (\r\n            <DropdownMenuItem\r\n              key={code}\r\n              onClick={() => handleCurrencyChange(code as CurrencyCode)}\r\n              className={`cursor-pointer mb-1 last:mb-0 p-2 rounded-sm ${\r\n                selectedCurrency === code ? 'bg-accent text-white' : 'hover:bg-gray-100'\r\n              }`}\r\n            >\r\n              <span className=\"text-xs\">\r\n                {currency.symbol} {currency.code}\r\n              </span>\r\n            </DropdownMenuItem>\r\n          ))}\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button \r\n          variant=\"outline\" \r\n          className={`flex items-center gap-4 ${className}`}\r\n        >\r\n          <DollarSign size={16} />\r\n          <span>{currencies[selectedCurrency].code}</span>\r\n          <span className=\"text-sm text-gray-500\">\r\n            ({currencies[selectedCurrency].symbol})\r\n          </span>\r\n          <ChevronDown size={16} />\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\" className=\"w-48 space-y-1 p-1 z-[60]\">\r\n        {Object.entries(currencies).map(([code, currency]) => (\r\n          <DropdownMenuItem\r\n            key={code}\r\n            onClick={() => handleCurrencyChange(code as CurrencyCode)}\r\n            className={`cursor-pointer flex items-center gap-2 justify-between mb-1 last:mb-0 ${\r\n              selectedCurrency === code ? 'bg-accent' : ''\r\n            }`}\r\n          >\r\n            <div className=\"flex items-center gap-4\">\r\n              <span className=\"font-medium\">{currency.symbol}</span>\r\n              <span>{currency.code}</span>\r\n            </div>\r\n            <span className=\"text-sm text-gray-500\">{currency.name}</span>\r\n          </DropdownMenuItem>\r\n        ))}\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n};\r\n\r\nexport default CurrencySelector;\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AAMA;AAAA;AAZA;;;;;;AAmBA,MAAM,mBAAoD,CAAC,EACzD,YAAY,EAAE,EACd,UAAU,SAAS,EACpB;IACC,MAAM,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IACtE,MAAM,aAAa;IAEnB,MAAM,uBAAuB,CAAC;QAC5B,YAAY;IACd;IAEA,IAAI,YAAY,WAAW;QACzB,qBACE,8OAAC,qIAAA,CAAA,eAAY;;8BACX,8OAAC,qIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAC,8EAA8E,EAAE,WAAW;;0CAEvG,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAChC,8OAAC;gCAAK,WAAU;0CAA4B,UAAU,CAAC,iBAAiB,CAAC,IAAI;;;;;;0CAC7E,8OAAC;gCAAK,WAAU;0CAAqB,UAAU,CAAC,iBAAiB,CAAC,MAAM;;;;;;0CACxE,8OAAC,oNAAA,CAAA,cAAW;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;;8BAGrC,8OAAC,qIAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAM,WAAU;8BACxC,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,MAAM,SAAS,iBAC/C,8OAAC,qIAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,qBAAqB;4BACpC,WAAW,CAAC,6CAA6C,EACvD,qBAAqB,OAAO,yBAAyB,qBACrD;sCAEF,cAAA,8OAAC;gCAAK,WAAU;;oCACb,SAAS,MAAM;oCAAC;oCAAE,SAAS,IAAI;;;;;;;2BAP7B;;;;;;;;;;;;;;;;IAcjB;IAEA,qBACE,8OAAC,qIAAA,CAAA,eAAY;;0BACX,8OAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAW,CAAC,wBAAwB,EAAE,WAAW;;sCAEjD,8OAAC,kNAAA,CAAA,aAAU;4BAAC,MAAM;;;;;;sCAClB,8OAAC;sCAAM,UAAU,CAAC,iBAAiB,CAAC,IAAI;;;;;;sCACxC,8OAAC;4BAAK,WAAU;;gCAAwB;gCACpC,UAAU,CAAC,iBAAiB,CAAC,MAAM;gCAAC;;;;;;;sCAExC,8OAAC,oNAAA,CAAA,cAAW;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAGvB,8OAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;0BACxC,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,MAAM,SAAS,iBAC/C,8OAAC,qIAAA,CAAA,mBAAgB;wBAEf,SAAS,IAAM,qBAAqB;wBACpC,WAAW,CAAC,sEAAsE,EAChF,qBAAqB,OAAO,cAAc,IAC1C;;0CAEF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAe,SAAS,MAAM;;;;;;kDAC9C,8OAAC;kDAAM,SAAS,IAAI;;;;;;;;;;;;0CAEtB,8OAAC;gCAAK,WAAU;0CAAyB,SAAS,IAAI;;;;;;;uBAVjD;;;;;;;;;;;;;;;;AAgBjB;uCAEe", "debugId": null}}, {"offset": {"line": 1379, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/contexts/WishlistContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, {\r\n  createContext,\r\n  useContext,\r\n  useState,\r\n  useEffect,\r\n  useCallback,\r\n} from \"react\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface WishlistContextType {\r\n  wishlist: string[];\r\n  wishlistItems: ProductData[];\r\n  addToWishlist: (productId: string) => void;\r\n  removeFromWishlist: (productId: string) => void;\r\n  isInWishlist: (productId: string) => boolean;\r\n  clearWishlist: () => void;\r\n  updateWishlistItems: (products: ProductData[]) => void;\r\n}\r\n\r\nconst WishlistContext = createContext<WishlistContextType | undefined>(\r\n  undefined\r\n);\r\n\r\nexport const WishlistProvider: React.FC<{ children: React.ReactNode }> = ({\r\n  children,\r\n}) => {\r\n  // Store only product IDs in the wishlist\r\n  const [wishlist, setWishlist] = useState<string[]>([]);\r\n  // Store the actual product data for display\r\n  const [wishlistItems, setWishlistItems] = useState<ProductData[]>([]);\r\n\r\n  // Load wishlist from localStorage on initial render\r\n  useEffect(() => {\r\n    const savedWishlist = localStorage.getItem(\"wishlist\");\r\n    if (savedWishlist) {\r\n      try {\r\n        const parsedWishlist = JSON.parse(savedWishlist);\r\n        if (Array.isArray(parsedWishlist)) {\r\n          setWishlist(parsedWishlist);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error parsing wishlist from localStorage:\", error);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Save wishlist to localStorage whenever it changes\r\n  useEffect(() => {\r\n    localStorage.setItem(\"wishlist\", JSON.stringify(wishlist));\r\n  }, [wishlist]);\r\n\r\n  // Add a product to the wishlist\r\n  const addToWishlist = (productId: string) => {\r\n    if (!isInWishlist(productId)) {\r\n      setWishlist((prev) => [...prev, productId]);\r\n      toast.success(\"Added to wishlist\");\r\n    }\r\n  };\r\n\r\n  // Remove a product from the wishlist\r\n  const removeFromWishlist = (productId: string) => {\r\n    setWishlist((prev) => prev.filter((id) => id !== productId));\r\n    toast.success(\"Removed from wishlist\");\r\n  };\r\n\r\n  // Check if a product is in the wishlist\r\n  const isInWishlist = (productId: string) => {\r\n    return wishlist.includes(productId);\r\n  };\r\n\r\n  // Clear the entire wishlist\r\n  const clearWishlist = () => {\r\n    setWishlist([]);\r\n    toast.success(\"Wishlist cleared\");\r\n  };\r\n\r\n  // Update wishlistItems when products are fetched - memoized with useCallback\r\n  const updateWishlistItems = useCallback(\r\n    (products: ProductData[]) => {\r\n      const items = products.filter((product) => wishlist.includes(product.id));\r\n      setWishlistItems(items);\r\n    },\r\n    [wishlist]\r\n  );\r\n\r\n  return (\r\n    <WishlistContext.Provider\r\n      value={{\r\n        wishlist,\r\n        wishlistItems,\r\n        addToWishlist,\r\n        removeFromWishlist,\r\n        isInWishlist,\r\n        clearWishlist,\r\n        updateWishlistItems,\r\n      }}\r\n    >\r\n      {children}\r\n    </WishlistContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useWishlist = () => {\r\n  const context = useContext(WishlistContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useWishlist must be used within a WishlistProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AAOA;AARA;;;;AAoBA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAClC;AAGK,MAAM,mBAA4D,CAAC,EACxE,QAAQ,EACT;IACC,yCAAyC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,4CAA4C;IAC5C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAEpE,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,IAAI;gBACF,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAClC,IAAI,MAAM,OAAO,CAAC,iBAAiB;oBACjC,YAAY;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;YAC7D;QACF;IACF,GAAG,EAAE;IAEL,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;IAClD,GAAG;QAAC;KAAS;IAEb,gCAAgC;IAChC,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,aAAa,YAAY;YAC5B,YAAY,CAAC,OAAS;uBAAI;oBAAM;iBAAU;YAC1C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,qCAAqC;IACrC,MAAM,qBAAqB,CAAC;QAC1B,YAAY,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,KAAO,OAAO;QACjD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,wCAAwC;IACxC,MAAM,eAAe,CAAC;QACpB,OAAO,SAAS,QAAQ,CAAC;IAC3B;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB;QACpB,YAAY,EAAE;QACd,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,6EAA6E;IAC7E,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,CAAC;QACC,MAAM,QAAQ,SAAS,MAAM,CAAC,CAAC,UAAY,SAAS,QAAQ,CAAC,QAAQ,EAAE;QACvE,iBAAiB;IACnB,GACA;QAAC;KAAS;IAGZ,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,MAAM,cAAc;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/ToolBar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { HiOutlineUser } from \"react-icons/hi2\";\r\nimport { IoHeartOutline } from \"react-icons/io5\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"./ui/button\";\r\nimport CartIcon from \"./cart/CartIcon\";\r\nimport CurrencySelector from \"./CurrencySelector\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { useWishlist } from \"@/contexts/WishlistContext\";\r\n\r\nconst ToolBar = () => {\r\n  const [showAuthMenu, setShowAuthMenu] = useState(false);\r\n  const { user, isAuthenticated, isLoading, logout } = useAuth();\r\n  const { wishlist } = useWishlist();\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: Event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setShowAuthMenu(false);\r\n      }\r\n    };\r\n\r\n    if (showAuthMenu) {\r\n      document.addEventListener('mousedown', handleClickOutside);\r\n      document.addEventListener('touchstart', handleClickOutside);\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n      document.removeEventListener('touchstart', handleClickOutside);\r\n    };\r\n  }, [showAuthMenu]);\r\n\r\n  const iconVariants = {\r\n    hover: {\r\n      scale: 1.2,\r\n      color: \"var(--accent)\",\r\n      transition: { type: \"spring\", stiffness: 400, damping: 10 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-row items-center text-foreground gap-1 sm:gap-2 md:gap-3 flex-shrink-0\">\r\n      <div className=\"relative overflow-visible\" ref={dropdownRef}>\r\n        <motion.div\r\n          className=\"relative cursor-pointer p-1.5 z-10 hover:bg-accent/10 rounded-full transition-colors min-w-[44px] min-h-[44px] flex items-center justify-center\"\r\n          whileHover=\"hover\"\r\n          variants={iconVariants}\r\n          onClick={() => setShowAuthMenu(!showAuthMenu)}\r\n          role=\"button\"\r\n          aria-label={showAuthMenu ? \"Close user menu\" : \"Open user menu\"}\r\n          aria-expanded={showAuthMenu}\r\n          aria-haspopup=\"menu\"\r\n          tabIndex={0}\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter' || e.key === ' ') {\r\n              e.preventDefault();\r\n              setShowAuthMenu(!showAuthMenu);\r\n            }\r\n          }}\r\n        >\r\n          <HiOutlineUser size={18} className=\"w-[18px] h-[18px] sm:w-5 sm:h-5\" />\r\n        </motion.div>\r\n\r\n        {/* Auth dropdown menu */}\r\n        <AnimatePresence>\r\n          {showAuthMenu && (\r\n            <motion.div\r\n              className=\"auth-dropdown fixed sm:absolute left-1/2 sm:right-0 sm:left-auto top-16 sm:top-auto mt-0 sm:mt-2 w-72 sm:w-56 md:w-48 bg-white rounded-lg shadow-xl py-2 z-[60] border border-gray-300 transform -translate-x-1/2 sm:translate-x-0 max-w-[calc(100vw-1rem)] sm:max-w-none backdrop-blur-sm\"\r\n              initial={{ opacity: 0, y: -10, scale: 0.95 }}\r\n              animate={{ opacity: 1, y: 0, scale: 1 }}\r\n              exit={{ opacity: 0, y: -10, scale: 0.95 }}\r\n              transition={{ duration: 0.2 }}\r\n              role=\"menu\"\r\n              aria-label=\"User account menu\"\r\n            >\r\n              <div className=\"px-4 py-2 border-b border-gray-200\">\r\n                <h3 className=\"text-sm font-medium text-gray-800\">\r\n                  {isLoading\r\n                    ? \"Loading...\"\r\n                    : isAuthenticated\r\n                    ? `Hello, ${user?.name?.split(\" \")[0]}`\r\n                    : \"Account\"}\r\n                </h3>\r\n              </div>\r\n\r\n              {isLoading ? (\r\n                <div className=\"px-4 py-4 text-sm text-gray-700 flex items-center justify-center\">\r\n                  <div className=\"h-4 w-4 border-2 border-gray-400 border-t-accent rounded-full animate-spin mr-2\"></div>\r\n                  <span>Authenticating...</span>\r\n                </div>\r\n              ) : !isAuthenticated ? (\r\n                <>\r\n                  <Link href=\"/sign-in\" className=\"block\" onClick={() => setShowAuthMenu(false)}>\r\n                    <motion.div\r\n                      className=\"px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2 text-gray-800\"\r\n                      whileHover={{ x: 5 }}\r\n                      role=\"menuitem\"\r\n                    >\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"2\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        className=\"text-gray-600\"\r\n                      >\r\n                        <path d=\"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4\" />\r\n                        <polyline points=\"10 17 15 12 10 7\" />\r\n                        <line x1=\"15\" y1=\"12\" x2=\"3\" y2=\"12\" />\r\n                      </svg>\r\n                      <span>Sign In</span>\r\n                    </motion.div>\r\n                  </Link>\r\n\r\n                  <Link href=\"/sign-up\" className=\"block\" onClick={() => setShowAuthMenu(false)}>\r\n                    <motion.div\r\n                      className=\"px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2 text-gray-800\"\r\n                      whileHover={{ x: 5 }}\r\n                      role=\"menuitem\"\r\n                    >\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"2\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        className=\"text-gray-600\"\r\n                      >\r\n                        <path d=\"M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\" />\r\n                        <circle cx=\"8.5\" cy=\"7\" r=\"4\" />\r\n                        <line x1=\"20\" y1=\"8\" x2=\"20\" y2=\"14\" />\r\n                        <line x1=\"23\" y1=\"11\" x2=\"17\" y2=\"11\" />\r\n                      </svg>\r\n                      <span>Sign Up</span>\r\n                    </motion.div>\r\n                  </Link>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Link href=\"/profile\" className=\"block\" onClick={() => setShowAuthMenu(false)}>\r\n                    <motion.div\r\n                      className=\"px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2 text-gray-800\"\r\n                      whileHover={{ x: 5 }}\r\n                      role=\"menuitem\"\r\n                    >\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"2\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        className=\"text-gray-600\"\r\n                      >\r\n                        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\" />\r\n                        <circle cx=\"12\" cy=\"7\" r=\"4\" />\r\n                      </svg>\r\n                      <span>My Profile</span>\r\n                    </motion.div>\r\n                  </Link>\r\n\r\n                  <motion.div\r\n                    className=\"px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2 cursor-pointer text-gray-800\"\r\n                    whileHover={{ x: 5 }}\r\n                    onClick={() => {\r\n                      logout();\r\n                      setShowAuthMenu(false);\r\n                    }}\r\n                    role=\"menuitem\"\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => {\r\n                      if (e.key === 'Enter' || e.key === ' ') {\r\n                        e.preventDefault();\r\n                        logout();\r\n                        setShowAuthMenu(false);\r\n                      }\r\n                    }}\r\n                  >\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      width=\"16\"\r\n                      height=\"16\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"2\"\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      className=\"text-gray-600\"\r\n                    >\r\n                      <path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\" />\r\n                      <polyline points=\"16 17 21 12 16 7\" />\r\n                      <line x1=\"21\" y1=\"12\" x2=\"9\" y2=\"12\" />\r\n                    </svg>\r\n                    <span>Sign Out</span>\r\n                  </motion.div>\r\n                </>\r\n              )}\r\n\r\n              {!isLoading && (\r\n                <div className=\"border-t border-gray-200 my-1\"></div>\r\n              )}\r\n\r\n              {!isLoading && (\r\n                <>\r\n                  <Link href=\"/orders\" className=\"block\" onClick={() => setShowAuthMenu(false)}>\r\n                    <motion.div\r\n                      className=\"px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2 text-gray-800\"\r\n                      whileHover={{ x: 5 }}\r\n                      role=\"menuitem\"\r\n                    >\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"2\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        className=\"text-gray-600\"\r\n                      >\r\n                        <path d=\"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z\" />\r\n                      </svg>\r\n                      <span>My Orders</span>\r\n                    </motion.div>\r\n                  </Link>\r\n\r\n                  <Link href=\"/wishlist\" className=\"block\" onClick={() => setShowAuthMenu(false)}>\r\n                    <motion.div\r\n                      className=\"px-4 py-2 text-sm hover:bg-accent/10 transition-colors flex items-center gap-2 text-gray-800\"\r\n                      whileHover={{ x: 5 }}\r\n                      role=\"menuitem\"\r\n                    >\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"2\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        className=\"text-gray-600\"\r\n                      >\r\n                        <path d=\"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\" />\r\n                      </svg>\r\n                      <span>My Wishlist</span>\r\n                      {wishlist.length > 0 && (\r\n                        <span className=\"ml-auto bg-accent text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center\">\r\n                          {wishlist.length > 9 ? \"9+\" : wishlist.length}\r\n                        </span>\r\n                      )}\r\n                    </motion.div>\r\n                  </Link>\r\n                </>\r\n              )}\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n\r\n      <Link href=\"/wishlist\" aria-label={`Wishlist with ${wishlist.length} items`}>\r\n        <motion.div\r\n          className=\"relative cursor-pointer p-1.5 hover:bg-accent/10 rounded-full transition-colors min-w-[40px] min-h-[40px] flex items-center justify-center\"\r\n          whileHover=\"hover\"\r\n          variants={iconVariants}\r\n        >\r\n          <IoHeartOutline size={18} className=\"w-[18px] h-[18px] sm:w-5 sm:h-5\" />\r\n          {wishlist.length > 0 && (\r\n            <span className=\"absolute -top-0.5 -right-0.5 bg-accent text-white text-xs font-bold rounded-full h-4 w-4 sm:h-5 sm:w-5 flex items-center justify-center text-[10px] sm:text-xs\">\r\n              {wishlist.length > 9 ? \"9+\" : wishlist.length}\r\n            </span>\r\n          )}\r\n        </motion.div>\r\n      </Link>\r\n\r\n      <CartIcon />\r\n\r\n      {/* Currency Selector */}\r\n      <div className=\"block\">\r\n        <CurrencySelector variant=\"compact\" />\r\n      </div>\r\n\r\n      {/* Direct Sign In/Sign Up buttons for desktop only */}\r\n      <div className=\"hidden lg:flex items-center gap-2\">\r\n        {isLoading ? (\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"h-4 w-4 border-2 border-gray-300 border-t-accent rounded-full animate-spin\"></div>\r\n            <span className=\"text-xs text-gray-500\">Loading...</span>\r\n          </div>\r\n        ) : !isAuthenticated ? (\r\n          <>\r\n            <Link href=\"/sign-in\">\r\n               <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"text-xs bg-[#d3691e] hover:bg-accent/90 border-white text-white cursor-pointer\"\r\n              >\r\n                Sign In\r\n              </Button>\r\n            </Link>\r\n            <Link href=\"/sign-up\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"text-xs bg-[#d3691e] hover:bg-accent/90 border-white text-white cursor-pointer\"\r\n              >\r\n                Sign Up\r\n              </Button>\r\n            </Link>\r\n          </>\r\n        ) : (\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-sm font-medium text-gray-700\">\r\n              Hello, {user?.name?.split(\" \")[0]}\r\n            </span>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"text-xs hover:bg-accent/10 hover:text-accent border-white\"\r\n              onClick={logout}\r\n            >\r\n              Sign Out\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ToolBar;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;;AAYA,MAAM,UAAU;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,gBAAgB;YAClB;QACF;QAEA,IAAI,cAAc;YAChB,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,cAAc;QAC1C;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,cAAc;QAC7C;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,eAAe;QACnB,OAAO;YACL,OAAO;YACP,OAAO;YACP,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;QAC5D;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;gBAA4B,KAAK;;kCAC9C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,YAAW;wBACX,UAAU;wBACV,SAAS,IAAM,gBAAgB,CAAC;wBAChC,MAAK;wBACL,cAAY,eAAe,oBAAoB;wBAC/C,iBAAe;wBACf,iBAAc;wBACd,UAAU;wBACV,WAAW,CAAC;4BACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;gCACtC,EAAE,cAAc;gCAChB,gBAAgB,CAAC;4BACnB;wBACF;kCAEA,cAAA,8OAAC,+IAAA,CAAA,gBAAa;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;kCAIrC,8OAAC,yLAAA,CAAA,kBAAe;kCACb,8BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;gCAAI,OAAO;4BAAK;4BAC3C,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAG,OAAO;4BAAE;4BACtC,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;gCAAI,OAAO;4BAAK;4BACxC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,MAAK;4BACL,cAAW;;8CAEX,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDACX,YACG,eACA,kBACA,CAAC,OAAO,EAAE,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE,EAAE,GACrC;;;;;;;;;;;gCAIP,0BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;2CAEN,CAAC,gCACH;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;4CAAQ,SAAS,IAAM,gBAAgB;sDACrE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,GAAG;gDAAE;gDACnB,MAAK;;kEAEL,8OAAC;wDACC,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,SAAQ;wDACR,MAAK;wDACL,QAAO;wDACP,aAAY;wDACZ,eAAc;wDACd,gBAAe;wDACf,WAAU;;0EAEV,8OAAC;gEAAK,GAAE;;;;;;0EACR,8OAAC;gEAAS,QAAO;;;;;;0EACjB,8OAAC;gEAAK,IAAG;gEAAK,IAAG;gEAAK,IAAG;gEAAI,IAAG;;;;;;;;;;;;kEAElC,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAIV,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;4CAAQ,SAAS,IAAM,gBAAgB;sDACrE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,GAAG;gDAAE;gDACnB,MAAK;;kEAEL,8OAAC;wDACC,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,SAAQ;wDACR,MAAK;wDACL,QAAO;wDACP,aAAY;wDACZ,eAAc;wDACd,gBAAe;wDACf,WAAU;;0EAEV,8OAAC;gEAAK,GAAE;;;;;;0EACR,8OAAC;gEAAO,IAAG;gEAAM,IAAG;gEAAI,GAAE;;;;;;0EAC1B,8OAAC;gEAAK,IAAG;gEAAK,IAAG;gEAAI,IAAG;gEAAK,IAAG;;;;;;0EAChC,8OAAC;gEAAK,IAAG;gEAAK,IAAG;gEAAK,IAAG;gEAAK,IAAG;;;;;;;;;;;;kEAEnC,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;iEAKZ;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;4CAAQ,SAAS,IAAM,gBAAgB;sDACrE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,GAAG;gDAAE;gDACnB,MAAK;;kEAEL,8OAAC;wDACC,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,SAAQ;wDACR,MAAK;wDACL,QAAO;wDACP,aAAY;wDACZ,eAAc;wDACd,gBAAe;wDACf,WAAU;;0EAEV,8OAAC;gEAAK,GAAE;;;;;;0EACR,8OAAC;gEAAO,IAAG;gEAAK,IAAG;gEAAI,GAAE;;;;;;;;;;;;kEAE3B,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAIV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,GAAG;4CAAE;4CACnB,SAAS;gDACP;gDACA,gBAAgB;4CAClB;4CACA,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC;gDACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;oDACtC,EAAE,cAAc;oDAChB;oDACA,gBAAgB;gDAClB;4CACF;;8DAEA,8OAAC;oDACC,OAAM;oDACN,OAAM;oDACN,QAAO;oDACP,SAAQ;oDACR,MAAK;oDACL,QAAO;oDACP,aAAY;oDACZ,eAAc;oDACd,gBAAe;oDACf,WAAU;;sEAEV,8OAAC;4DAAK,GAAE;;;;;;sEACR,8OAAC;4DAAS,QAAO;;;;;;sEACjB,8OAAC;4DAAK,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAI,IAAG;;;;;;;;;;;;8DAElC,8OAAC;8DAAK;;;;;;;;;;;;;;gCAKX,CAAC,2BACA,8OAAC;oCAAI,WAAU;;;;;;gCAGhB,CAAC,2BACA;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;4CAAQ,SAAS,IAAM,gBAAgB;sDACpE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,GAAG;gDAAE;gDACnB,MAAK;;kEAEL,8OAAC;wDACC,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,SAAQ;wDACR,MAAK;wDACL,QAAO;wDACP,aAAY;wDACZ,eAAc;wDACd,gBAAe;wDACf,WAAU;kEAEV,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;kEAEV,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAIV,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;4CAAQ,SAAS,IAAM,gBAAgB;sDACtE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,GAAG;gDAAE;gDACnB,MAAK;;kEAEL,8OAAC;wDACC,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,SAAQ;wDACR,MAAK;wDACL,QAAO;wDACP,aAAY;wDACZ,eAAc;wDACd,gBAAe;wDACf,WAAU;kEAEV,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;kEAEV,8OAAC;kEAAK;;;;;;oDACL,SAAS,MAAM,GAAG,mBACjB,8OAAC;wDAAK,WAAU;kEACb,SAAS,MAAM,GAAG,IAAI,OAAO,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjE,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;gBAAY,cAAY,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,MAAM,CAAC;0BACzE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,YAAW;oBACX,UAAU;;sCAEV,8OAAC,+IAAA,CAAA,iBAAc;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBACnC,SAAS,MAAM,GAAG,mBACjB,8OAAC;4BAAK,WAAU;sCACb,SAAS,MAAM,GAAG,IAAI,OAAO,SAAS,MAAM;;;;;;;;;;;;;;;;;0BAMrD,8OAAC,+HAAA,CAAA,UAAQ;;;;;0BAGT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,+HAAA,CAAA,UAAgB;oBAAC,SAAQ;;;;;;;;;;;0BAI5B,8OAAC;gBAAI,WAAU;0BACZ,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;2BAExC,CAAC,gCACH;;sCACE,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACR,cAAA,8OAAC,2HAAA,CAAA,SAAM;gCACN,SAAQ;gCACR,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;sCAIH,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;iDAML,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;;gCAAoC;gCAC1C,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE;;;;;;;sCAEnC,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 2238, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/averia_serif_libre_33e0580e.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"averia_serif_libre_33e0580e-module__hVGtea__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 2247, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/averia_serif_libre_33e0580e.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22fonts.ts%22,%22import%22:%22Averia_Serif_Libre%22,%22arguments%22:[{%22weight%22:[%22300%22,%22400%22,%22700%22],%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22fallback%22:[%22Times%20New%20Roman%22,%22serif%22]}],%22variableName%22:%22averia_serif_libre%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Averia Serif Libre', Times New Roman, serif\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,kKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,kKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,kKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2269, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/constants/fonts.ts"], "sourcesContent": ["import { Averia_Serif_Libre } from \"next/font/google\";\r\n\r\nconst averia_serif_libre = Averia_Serif_Libre({\r\n  weight: [\"300\", \"400\", \"700\"],\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n  fallback: [\"Times New Roman\", \"serif\"],\r\n});\r\n\r\nexport const secondaryFont =\r\n  \" capitalize \" + averia_serif_libre.className + \" \";\r\n"], "names": [], "mappings": ";;;;;AASO,MAAM,gBACX,iBAAiB,sJAAA,CAAA,UAAkB,CAAC,SAAS,GAAG", "debugId": null}}, {"offset": {"line": 2281, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/ui/HamburgerMenu.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\n\ninterface HamburgerMenuProps {\n  isOpen: boolean;\n  onClick: () => void;\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  color?: string;\n  ariaLabel?: string;\n}\n\nconst HamburgerMenu: React.FC<HamburgerMenuProps> = ({\n  isOpen,\n  onClick,\n  className = \"\",\n  size = \"md\",\n  color = \"currentColor\",\n  ariaLabel = \"Toggle navigation menu\",\n}) => {\n  const sizeClasses = {\n    sm: \"w-5 h-5\",\n    md: \"w-6 h-6\",\n    lg: \"w-7 h-7\",\n  };\n\n  const lineWidths = {\n    sm: \"16px\",\n    md: \"20px\",\n    lg: \"24px\",\n  };\n\n  const lineHeight = \"2px\";\n  const spacing = size === \"sm\" ? \"3px\" : size === \"md\" ? \"4px\" : \"5px\";\n\n  return (\n    <button\n      onClick={onClick}\n      onKeyDown={(e) => {\n        if (e.key === \"Enter\" || e.key === \" \") {\n          e.preventDefault();\n          onClick();\n        }\n      }}\n      className={`\n        touch-target\n        flex items-center justify-center\n        p-2 rounded-md\n        hover:bg-accent/10\n        focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2\n        transition-colors duration-200\n        ${sizeClasses[size]}\n        ${className}\n      `}\n      aria-label={ariaLabel}\n      aria-expanded={isOpen}\n      aria-controls=\"mobile-navigation\"\n      aria-haspopup=\"true\"\n      type=\"button\"\n      role=\"button\"\n      tabIndex={0}\n    >\n      <div className=\"relative flex flex-col justify-center items-center\">\n        {/* Top line */}\n        <motion.span\n          className=\"block bg-current\"\n          style={{\n            width: lineWidths[size],\n            height: lineHeight,\n            backgroundColor: color,\n          }}\n          animate={{\n            rotate: isOpen ? 45 : 0,\n            y: isOpen ? `calc(${lineHeight} + ${spacing})` : 0,\n          }}\n          transition={{\n            duration: 0.3,\n            ease: \"easeInOut\",\n          }}\n        />\n        \n        {/* Middle line */}\n        <motion.span\n          className=\"block bg-current\"\n          style={{\n            width: lineWidths[size],\n            height: lineHeight,\n            backgroundColor: color,\n            marginTop: spacing,\n            marginBottom: spacing,\n          }}\n          animate={{\n            opacity: isOpen ? 0 : 1,\n            scale: isOpen ? 0.8 : 1,\n          }}\n          transition={{\n            duration: 0.3,\n            ease: \"easeInOut\",\n          }}\n        />\n        \n        {/* Bottom line */}\n        <motion.span\n          className=\"block bg-current\"\n          style={{\n            width: lineWidths[size],\n            height: lineHeight,\n            backgroundColor: color,\n          }}\n          animate={{\n            rotate: isOpen ? -45 : 0,\n            y: isOpen ? `calc(-${lineHeight} - ${spacing})` : 0,\n          }}\n          transition={{\n            duration: 0.3,\n            ease: \"easeInOut\",\n          }}\n        />\n      </div>\n    </button>\n  );\n};\n\nexport default HamburgerMenu;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,gBAA8C,CAAC,EACnD,MAAM,EACN,OAAO,EACP,YAAY,EAAE,EACd,OAAO,IAAI,EACX,QAAQ,cAAc,EACtB,YAAY,wBAAwB,EACrC;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,aAAa;IACnB,MAAM,UAAU,SAAS,OAAO,QAAQ,SAAS,OAAO,QAAQ;IAEhE,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,CAAC;YACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;gBACtC,EAAE,cAAc;gBAChB;YACF;QACF;QACA,WAAW,CAAC;;;;;;;QAOV,EAAE,WAAW,CAAC,KAAK,CAAC;QACpB,EAAE,UAAU;MACd,CAAC;QACD,cAAY;QACZ,iBAAe;QACf,iBAAc;QACd,iBAAc;QACd,MAAK;QACL,MAAK;QACL,UAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,WAAU;oBACV,OAAO;wBACL,OAAO,UAAU,CAAC,KAAK;wBACvB,QAAQ;wBACR,iBAAiB;oBACnB;oBACA,SAAS;wBACP,QAAQ,SAAS,KAAK;wBACtB,GAAG,SAAS,CAAC,KAAK,EAAE,WAAW,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG;oBACnD;oBACA,YAAY;wBACV,UAAU;wBACV,MAAM;oBACR;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,WAAU;oBACV,OAAO;wBACL,OAAO,UAAU,CAAC,KAAK;wBACvB,QAAQ;wBACR,iBAAiB;wBACjB,WAAW;wBACX,cAAc;oBAChB;oBACA,SAAS;wBACP,SAAS,SAAS,IAAI;wBACtB,OAAO,SAAS,MAAM;oBACxB;oBACA,YAAY;wBACV,UAAU;wBACV,MAAM;oBACR;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,WAAU;oBACV,OAAO;wBACL,OAAO,UAAU,CAAC,KAAK;wBACvB,QAAQ;wBACR,iBAAiB;oBACnB;oBACA,SAAS;wBACP,QAAQ,SAAS,CAAC,KAAK;wBACvB,GAAG,SAAS,CAAC,MAAM,EAAE,WAAW,GAAG,EAAE,QAAQ,CAAC,CAAC,GAAG;oBACpD;oBACA,YAAY;wBACV,UAAU;wBACV,MAAM;oBACR;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2411, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/navigation/MobileSidebar.tsx"], "sourcesContent": ["\"use client\";\nimport React, { useEffect, useRef } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { X, Home, ShoppingBag, Heart, User, Phone, Info } from \"lucide-react\";\nimport { secondaryFont } from \"@/constants/fonts\";\n\ninterface NavItem {\n  name: string;\n  path: string;\n  icon?: React.ReactNode;\n}\n\ninterface MobileSidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n  navItems?: NavItem[];\n}\n\nconst MobileSidebar: React.FC<MobileSidebarProps> = ({\n  isOpen,\n  onClose,\n  navItems = [\n    { name: \"Home\", path: \"/\", icon: <Home size={20} /> },\n    { name: \"About Us\", path: \"/about-us\", icon: <Info size={20} /> },\n    { name: \"Contact Us\", path: \"/contact\", icon: <Phone size={20} /> },\n    { name: \"Products\", path: \"/products\", icon: <ShoppingBag size={20} /> },\n    { name: \"Wishlist\", path: \"/wishlist\", icon: <Heart size={20} /> },\n  ],\n}) => {\n  const pathname = usePathname();\n  const sidebarRef = useRef<HTMLDivElement>(null);\n\n  // Handle escape key press\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === \"Escape\" && isOpen) {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener(\"keydown\", handleEscape);\n      // Prevent body scroll when sidebar is open\n      document.body.classList.add(\"no-scroll\");\n    } else {\n      document.body.classList.remove(\"no-scroll\");\n    }\n\n    return () => {\n      document.removeEventListener(\"keydown\", handleEscape);\n      document.body.classList.remove(\"no-scroll\");\n    };\n  }, [isOpen, onClose]);\n\n  // Focus management and focus trap\n  useEffect(() => {\n    if (isOpen && sidebarRef.current) {\n      const sidebar = sidebarRef.current;\n      const focusableElements = sidebar.querySelectorAll(\n        'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n      ) as NodeListOf<HTMLElement>;\n\n      const firstFocusableElement = focusableElements[0];\n      const lastFocusableElement = focusableElements[focusableElements.length - 1];\n\n      // Focus the first element when sidebar opens\n      if (firstFocusableElement) {\n        firstFocusableElement.focus();\n      }\n\n      // Handle tab key for focus trap\n      const handleTabKey = (event: KeyboardEvent) => {\n        if (event.key === \"Tab\") {\n          if (event.shiftKey) {\n            // Shift + Tab\n            if (document.activeElement === firstFocusableElement) {\n              event.preventDefault();\n              lastFocusableElement?.focus();\n            }\n          } else {\n            // Tab\n            if (document.activeElement === lastFocusableElement) {\n              event.preventDefault();\n              firstFocusableElement?.focus();\n            }\n          }\n        }\n      };\n\n      sidebar.addEventListener(\"keydown\", handleTabKey);\n\n      return () => {\n        sidebar.removeEventListener(\"keydown\", handleTabKey);\n      };\n    }\n  }, [isOpen]);\n\n  const sidebarVariants = {\n    closed: {\n      x: \"-100%\",\n      transition: {\n        type: \"tween\",\n        duration: 0.3,\n        ease: \"easeInOut\",\n      },\n    },\n    open: {\n      x: 0,\n      transition: {\n        type: \"tween\",\n        duration: 0.3,\n        ease: \"easeInOut\",\n      },\n    },\n  };\n\n  const overlayVariants = {\n    closed: {\n      opacity: 0,\n      transition: {\n        duration: 0.3,\n      },\n    },\n    open: {\n      opacity: 1,\n      transition: {\n        duration: 0.3,\n      },\n    },\n  };\n\n  const itemVariants = {\n    closed: {\n      x: -20,\n      opacity: 0,\n    },\n    open: (i: number) => ({\n      x: 0,\n      opacity: 1,\n      transition: {\n        delay: i * 0.1,\n        duration: 0.3,\n        ease: \"easeOut\",\n      },\n    }),\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Overlay */}\n          <motion.div\n            className=\"fixed inset-0 bg-black/50 z-40 md:hidden\"\n            variants={overlayVariants}\n            initial=\"closed\"\n            animate=\"open\"\n            exit=\"closed\"\n            onClick={onClose}\n            aria-hidden=\"true\"\n          />\n\n          {/* Sidebar */}\n          <motion.div\n            ref={sidebarRef}\n            className=\"fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-50 md:hidden\"\n            variants={sidebarVariants}\n            initial=\"closed\"\n            animate=\"open\"\n            exit=\"closed\"\n            role=\"dialog\"\n            aria-modal=\"true\"\n            aria-labelledby=\"mobile-nav-title\"\n          >\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n              <h2\n                id=\"mobile-nav-title\"\n                className={`${secondaryFont} text-xl font-bold text-gray-800`}\n              >\n                <span className=\"text-accent\">Chinioti</span>{\" \"}\n                <span className=\"text-gray-800\">Wooden Art</span>\n              </h2>\n              <button\n                onClick={onClose}\n                className=\"touch-target p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 transition-colors\"\n                aria-label=\"Close navigation menu\"\n              >\n                <X size={24} className=\"text-gray-600\" />\n              </button>\n            </div>\n\n            {/* Navigation Items */}\n            <nav className=\"flex-1 px-4 py-6\" role=\"navigation\" aria-label=\"Main navigation\">\n              <ul className=\"space-y-2\" role=\"menu\" aria-orientation=\"vertical\">\n                {navItems.map((item, index) => {\n                  const isActive = pathname === item.path;\n                  \n                  return (\n                    <motion.li\n                      key={item.path}\n                      variants={itemVariants}\n                      initial=\"closed\"\n                      animate=\"open\"\n                      custom={index}\n                    >\n                      <Link\n                        href={item.path}\n                        onClick={onClose}\n                        onKeyDown={(e) => {\n                          if (e.key === \"Enter\" || e.key === \" \") {\n                            e.preventDefault();\n                            onClose();\n                          }\n                        }}\n                        className={`\n                          mobile-nav-item\n                          w-full rounded-lg\n                          text-left font-medium\n                          transition-all duration-200\n                          focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2\n                          ${\n                            isActive\n                              ? \"bg-accent text-white shadow-md\"\n                              : \"text-gray-700 hover:bg-accent/10 hover:text-accent\"\n                          }\n                        `}\n                        role=\"menuitem\"\n                        tabIndex={0}\n                        aria-current={isActive ? \"page\" : undefined}\n                      >\n                        <div className=\"flex items-center space-x-3\">\n                          {item.icon && (\n                            <span className={isActive ? \"text-white\" : \"text-gray-500\"}>\n                              {item.icon}\n                            </span>\n                          )}\n                          <span>{item.name}</span>\n                        </div>\n                      </Link>\n                    </motion.li>\n                  );\n                })}\n              </ul>\n            </nav>\n\n            {/* Footer */}\n            <div className=\"p-4 border-t border-gray-200\">\n              <p className=\"text-xs text-gray-500 text-center\">\n                © 2025 Chinioti Wooden Art\n              </p>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default MobileSidebar;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;;AAoBA,MAAM,gBAA8C,CAAC,EACnD,MAAM,EACN,OAAO,EACP,WAAW;IACT;QAAE,MAAM;QAAQ,MAAM;QAAK,oBAAM,8OAAC,mMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;IAAO;IACpD;QAAE,MAAM;QAAY,MAAM;QAAa,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;IAAO;IAChE;QAAE,MAAM;QAAc,MAAM;QAAY,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,MAAM;;;;;;IAAO;IAClE;QAAE,MAAM;QAAY,MAAM;QAAa,oBAAM,8OAAC,oNAAA,CAAA,cAAW;YAAC,MAAM;;;;;;IAAO;IACvE;QAAE,MAAM;QAAY,MAAM;QAAa,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,MAAM;;;;;;IAAO;CAClE,EACF;IACC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;gBACpC;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;YACrC,2CAA2C;YAC3C,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAC9B,OAAO;YACL,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACjC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,WAAW,OAAO,EAAE;YAChC,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,oBAAoB,QAAQ,gBAAgB,CAChD;YAGF,MAAM,wBAAwB,iBAAiB,CAAC,EAAE;YAClD,MAAM,uBAAuB,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;YAE5E,6CAA6C;YAC7C,IAAI,uBAAuB;gBACzB,sBAAsB,KAAK;YAC7B;YAEA,gCAAgC;YAChC,MAAM,eAAe,CAAC;gBACpB,IAAI,MAAM,GAAG,KAAK,OAAO;oBACvB,IAAI,MAAM,QAAQ,EAAE;wBAClB,cAAc;wBACd,IAAI,SAAS,aAAa,KAAK,uBAAuB;4BACpD,MAAM,cAAc;4BACpB,sBAAsB;wBACxB;oBACF,OAAO;wBACL,MAAM;wBACN,IAAI,SAAS,aAAa,KAAK,sBAAsB;4BACnD,MAAM,cAAc;4BACpB,uBAAuB;wBACzB;oBACF;gBACF;YACF;YAEA,QAAQ,gBAAgB,CAAC,WAAW;YAEpC,OAAO;gBACL,QAAQ,mBAAmB,CAAC,WAAW;YACzC;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,kBAAkB;QACtB,QAAQ;YACN,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,UAAU;gBACV,MAAM;YACR;QACF;QACA,MAAM;YACJ,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YACN,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;QACA,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,GAAG,CAAC;YACJ,SAAS;QACX;QACA,MAAM,CAAC,IAAc,CAAC;gBACpB,GAAG;gBACH,SAAS;gBACT,YAAY;oBACV,OAAO,IAAI;oBACX,UAAU;oBACV,MAAM;gBACR;YACF,CAAC;IACH;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,eAAY;;;;;;8BAId,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,MAAK;oBACL,cAAW;oBACX,mBAAgB;;sCAGhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,IAAG;oCACH,WAAW,GAAG,kHAAA,CAAA,gBAAa,CAAC,gCAAgC,CAAC;;sDAE7D,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;sDAC9C,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAElC,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAK3B,8OAAC;4BAAI,WAAU;4BAAmB,MAAK;4BAAa,cAAW;sCAC7D,cAAA,8OAAC;gCAAG,WAAU;gCAAY,MAAK;gCAAO,oBAAiB;0CACpD,SAAS,GAAG,CAAC,CAAC,MAAM;oCACnB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCAER,UAAU;wCACV,SAAQ;wCACR,SAAQ;wCACR,QAAQ;kDAER,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,SAAS;4CACT,WAAW,CAAC;gDACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;oDACtC,EAAE,cAAc;oDAChB;gDACF;4CACF;4CACA,WAAW,CAAC;;;;;;0BAMV,EACE,WACI,mCACA,qDACL;wBACH,CAAC;4CACD,MAAK;4CACL,UAAU;4CACV,gBAAc,WAAW,SAAS;sDAElC,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI,kBACR,8OAAC;wDAAK,WAAW,WAAW,eAAe;kEACxC,KAAK,IAAI;;;;;;kEAGd,8OAAC;kEAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;uCArCf,KAAK,IAAI;;;;;gCA0CpB;;;;;;;;;;;sCAKJ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;;;AAS/D;uCAEe", "debugId": null}}, {"offset": {"line": 2796, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/sections/Header.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport ToolBar from \"../components/ToolBar\";\r\nimport { secondaryFont } from \"@/constants/fonts\";\r\nimport { motion } from \"framer-motion\";\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport HamburgerMenu from \"../components/ui/HamburgerMenu\";\r\nimport MobileSidebar from \"../components/navigation/MobileSidebar\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport {\r\n  Home,\r\n  Info,\r\n  Phone,\r\n  FileText,\r\n  ShoppingBag,\r\n  Heart,\r\n  User,\r\n} from \"lucide-react\";\r\n\r\nconst Header = () => {\r\n  const pathname = usePathname();\r\n  const { isAuthenticated } = useAuth();\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n\r\n  const navItems = [\r\n    { name: \"About Us\", path: \"/about-us\" },\r\n    { name: \"Contact Us\", path: \"/contact\" },\r\n    // { name: \"VLOGS\", path: \"/video-blogs\" },\r\n    { name: \"Blogs\", path: \"/blogs\" },\r\n  ];\r\n\r\n  // Base mobile navigation items\r\n  const baseMobileNavItems = [\r\n    { name: \"Home\", path: \"/\", icon: <Home size={20} /> },\r\n    { name: \"About Us\", path: \"/about-us\", icon: <Info size={20} /> },\r\n    { name: \"Contact Us\", path: \"/contact\", icon: <Phone size={20} /> },\r\n    { name: \"Blogs\", path: \"/blogs\", icon: <FileText size={20} /> },\r\n    { name: \"Products\", path: \"/products\", icon: <ShoppingBag size={20} /> },\r\n    { name: \"Wishlist\", path: \"/wishlist\", icon: <Heart size={20} /> },\r\n  ];\r\n\r\n  // Add profile tab only if user is authenticated\r\n  const mobileNavItems = isAuthenticated\r\n    ? [\r\n        ...baseMobileNavItems,\r\n        { name: \"Profile\", path: \"/profile\", icon: <User size={20} /> },\r\n      ]\r\n    : baseMobileNavItems;\r\n\r\n  const toggleMobileSidebar = () => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  };\r\n\r\n  const closeMobileSidebar = () => {\r\n    setIsMobileSidebarOpen(false);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <motion.header\r\n        className=\"bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50\"\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        {/* Main header */}\r\n        <div className=\"responsive-header container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex items-center justify-between min-h-[56px] sm:min-h-[64px] md:min-h-[72px] py-2 sm:py-3\">\r\n            {/* Mobile hamburger menu */}\r\n            <div className=\"md:hidden flex-shrink-0\">\r\n              <HamburgerMenu\r\n                isOpen={isMobileSidebarOpen}\r\n                onClick={toggleMobileSidebar}\r\n                size=\"md\"\r\n                className=\"text-gray-700 hover:text-accent\"\r\n                ariaLabel=\"Toggle navigation menu\"\r\n              />\r\n            </div>\r\n\r\n            {/* Logo */}\r\n            <Link href=\"/\" className=\"flex-shrink-0 mx-auto md:mx-0\">\r\n              <motion.div\r\n                className={`${secondaryFont} responsive-logo font-bold relative`}\r\n                whileHover={{ scale: 1.02 }}\r\n                transition={{ type: \"spring\", stiffness: 300 }}\r\n              >\r\n                <span className=\"text-accent\">Chinioti</span>{\" \"}\r\n                <span className=\"text-gray-800 hidden md:inline\">\r\n                  Wooden Art\r\n                </span>\r\n                <span className=\"text-gray-800 inline md:hidden\">WA</span>\r\n                <motion.div\r\n                  className=\"absolute -bottom-1 left-0 h-0.5 bg-accent/30\"\r\n                  initial={{ width: \"0%\" }}\r\n                  whileHover={{ width: \"100%\" }}\r\n                  transition={{ duration: 0.3 }}\r\n                />\r\n              </motion.div>\r\n            </Link>\r\n\r\n            {/* Navigation - Desktop */}\r\n            <nav className=\"hidden md:flex items-center space-x-6 lg:space-x-8\">\r\n              {navItems.map((item) => (\r\n                <Link\r\n                  key={item.path}\r\n                  href={item.path}\r\n                  className={`relative text-sm font-medium transition-colors duration-200 whitespace-nowrap ${\r\n                    pathname === item.path\r\n                      ? \"text-accent\"\r\n                      : \"text-gray-700 hover:text-accent\"\r\n                  }`}\r\n                >\r\n                  {item.name}\r\n                  {pathname === item.path && (\r\n                    <motion.div\r\n                      className=\"absolute -bottom-1 left-0 right-0 h-0.5 bg-accent\"\r\n                      layoutId=\"activeTab\"\r\n                    />\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            {/* Right side - Shop Now & Toolbar */}\r\n            <div className=\"flex items-center space-x-1 sm:space-x-2 md:space-x-3 flex-shrink-0\">\r\n              <Link\r\n                href=\"/products\"\r\n                className=\"hidden lg:inline-flex items-center px-3 py-1.5 border-white bg-accent text-white text-xs font-medium rounded-md hover:bg-accent/90 transition-colors duration-200 whitespace-nowrap\"\r\n              >\r\n                Shop Now\r\n              </Link>\r\n              <ToolBar />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.header>\r\n\r\n      {/* Mobile Sidebar Navigation */}\r\n      <MobileSidebar\r\n        isOpen={isMobileSidebarOpen}\r\n        onClose={closeMobileSidebar}\r\n        navItems={mobileNavItems}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;;;AAoBA,MAAM,SAAS;IACb,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAClC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,WAAW;QACf;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC,2CAA2C;QAC3C;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,+BAA+B;IAC/B,MAAM,qBAAqB;QACzB;YAAE,MAAM;YAAQ,MAAM;YAAK,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;QAAO;QACpD;YAAE,MAAM;YAAY,MAAM;YAAa,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;QAAO;QAChE;YAAE,MAAM;YAAc,MAAM;YAAY,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;QAAO;QAClE;YAAE,MAAM;YAAS,MAAM;YAAU,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;QAAO;QAC9D;YAAE,MAAM;YAAY,MAAM;YAAa,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,MAAM;;;;;;QAAO;QACvE;YAAE,MAAM;YAAY,MAAM;YAAa,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;QAAO;KAClE;IAED,gDAAgD;IAChD,MAAM,iBAAiB,kBACnB;WACK;QACH;YAAE,MAAM;YAAW,MAAM;YAAY,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;QAAO;KAC/D,GACD;IAEJ,MAAM,sBAAsB;QAC1B,uBAAuB,CAAC;IAC1B;IAEA,MAAM,qBAAqB;QACzB,uBAAuB;IACzB;IAEA,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAG5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,UAAa;oCACZ,QAAQ;oCACR,SAAS;oCACT,MAAK;oCACL,WAAU;oCACV,WAAU;;;;;;;;;;;0CAKd,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAW,GAAG,kHAAA,CAAA,gBAAa,CAAC,mCAAmC,CAAC;oCAChE,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,MAAM;wCAAU,WAAW;oCAAI;;sDAE7C,8OAAC;4CAAK,WAAU;sDAAc;;;;;;wCAAgB;sDAC9C,8OAAC;4CAAK,WAAU;sDAAiC;;;;;;sDAGjD,8OAAC;4CAAK,WAAU;sDAAiC;;;;;;sDACjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAK;4CACvB,YAAY;gDAAE,OAAO;4CAAO;4CAC5B,YAAY;gDAAE,UAAU;4CAAI;;;;;;;;;;;;;;;;;0CAMlC,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,8EAA8E,EACxF,aAAa,KAAK,IAAI,GAClB,gBACA,mCACJ;;4CAED,KAAK,IAAI;4CACT,aAAa,KAAK,IAAI,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,UAAS;;;;;;;uCAZR,KAAK,IAAI;;;;;;;;;;0CAoBpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,sHAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhB,8OAAC,0IAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,SAAS;gBACT,UAAU;;;;;;;;AAIlB;uCAEe", "debugId": null}}, {"offset": {"line": 3124, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/apis/orders.ts"], "sourcesContent": ["import apiClient from \"@/lib/api/apiClient\";\r\nimport { Order, OrderStatus } from \"@/types/order\";\r\nimport { CartItem } from \"@/types/cart\";\r\n\r\n// Map frontend order to backend format\r\nconst mapFrontendOrderToBackend = (\r\n  order: Omit<Order, \"id\" | \"createdAt\">,\r\n  userId: string\r\n) => {\r\n  // Validate order items\r\n  if (!order.items || !Array.isArray(order.items)) {\r\n    console.error(\"Invalid order items:\", order.items);\r\n    throw new Error(\"Order items must be an array\");\r\n  }\r\n\r\n  // Filter out any invalid items\r\n  const validItems = order.items.filter(\r\n    (item) => item && item.product && item.product.id\r\n  );\r\n\r\n  if (validItems.length === 0) {\r\n    console.error(\"No valid items in order\");\r\n    throw new Error(\"Order must contain at least one valid item\");\r\n  }\r\n\r\n  // Create items array in the format expected by backend\r\n  const items = validItems.map((item) => {\r\n    // Get the best available image\r\n    let imageUrl = \"https://via.placeholder.com/150\"; // Default placeholder\r\n    if (item.product.images && item.product.images.length > 0) {\r\n      imageUrl = item.product.images[0];\r\n    } else if (item.product.image) {\r\n      imageUrl = item.product.image;\r\n    }\r\n\r\n    return {\r\n      productId: item.product.id,\r\n      name: item.product.title || item.product.name || \"Product\",\r\n      price: item.product.price || 0,\r\n      quantity: item.quantity,\r\n      image: imageUrl,\r\n    };\r\n  });\r\n\r\n  // Create shipping address object\r\n  const shippingAddress = {\r\n    name: `${order.customer.firstName} ${order.customer.lastName}`,\r\n    address: order.shipping.address,\r\n    city: order.shipping.city,\r\n    postalCode: order.shipping.postalCode || \"\",\r\n    country: \"Pakistan\", // Default country, can be made configurable\r\n    phone: order.customer.phone,\r\n  };\r\n\r\n  return {\r\n    user: userId,\r\n    items: items,\r\n    total: order.total,\r\n    status: order.status,\r\n    shippingAddress: shippingAddress,\r\n    paymentMethod: order.paymentMethod === \"cash\" ? \"cash_on_delivery\" :\r\n                   order.paymentMethod === \"bank\" ? \"bank_transfer\" : \"cash_on_delivery\",\r\n    notes: \"\", // Can be added to frontend form if needed\r\n    // Additional metadata that might be useful for the backend\r\n    metadata: {\r\n      customer: order.customer,\r\n      shipping: order.shipping,\r\n      paymentMethod: order.paymentMethod,\r\n      subtotal: order.subtotal,\r\n      discount: order.discount,\r\n    },\r\n  };\r\n};\r\n\r\n// Map backend order to frontend format\r\nconst mapBackendOrderToFrontend = (backendOrder: any): Order => {\r\n  if (!backendOrder) {\r\n    console.error(\"Received null or undefined backendOrder\");\r\n    return {\r\n      id: \"\",\r\n      items: [],\r\n      total: 0,\r\n      subtotal: 0,\r\n      discount: 0,\r\n      customer: {\r\n        firstName: \"\",\r\n        lastName: \"\",\r\n        email: \"\",\r\n        phone: \"\",\r\n      },\r\n      shipping: {\r\n        address: \"\",\r\n        city: \"\",\r\n        postalCode: \"\",\r\n      },\r\n      paymentMethod: \"cash\",\r\n      status: \"pending\",\r\n      createdAt: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  // Extract metadata (or use defaults if not available)\r\n  const metadata = backendOrder.metadata || {};\r\n\r\n  // Log the order data for debugging\r\n  console.log(\"Backend order data:\", backendOrder);\r\n  console.log(\"Metadata:\", metadata);\r\n\r\n  // For orders created before metadata was added, try to reconstruct from products\r\n  let items = [];\r\n\r\n  // First try to get items directly from the order (new format)\r\n  if (backendOrder.items && Array.isArray(backendOrder.items)) {\r\n    items = backendOrder.items.map((item: any) => ({\r\n      product: {\r\n        id: item.productId || \"\",\r\n        title: item.name || \"Product\",\r\n        price: item.price || 0,\r\n        images: item.image ? [item.image] : [],\r\n        image: item.image || \"\",      },\r\n      quantity: item.quantity || 1,\r\n    }));\r\n  }\r\n  // If no items found, try to get items from metadata\r\n  else if (metadata.items && Array.isArray(metadata.items)) {\r\n    // Validate each item has a product property\r\n    items = metadata.items\r\n      .filter((item:any) => item && item.product)\r\n      .map((item:any) => ({\r\n        product: {\r\n          id: item.product.id || item.productId || \"\",\r\n          title: item.product.title || \"Product\",\r\n          price: item.product.price || 0,\r\n          images: item.product.images || [],\r\n          image: item.product.image || \"\",\r\n        },\r\n        quantity: item.quantity || 1,\r\n      }));\r\n  }\r\n  // If no items found in metadata, try to reconstruct from products (legacy)\r\n  else if (\r\n    backendOrder.products &&\r\n    Array.isArray(backendOrder.products)\r\n  ) {\r\n    // If we have populated products from the backend\r\n    backendOrder.products.forEach((product: any) => {\r\n      if (product) {\r\n        items.push({\r\n          product: {\r\n            id: product._id || \"\",\r\n            title: product.name || \"Product\",\r\n            price: product.price || 0,\r\n            images: product.images || (product.image ? [product.image] : []),\r\n            image: product.image || \"\",\r\n          },\r\n          quantity: 1, // Default quantity since we don't know the actual quantity\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  return {\r\n    id: backendOrder._id || \"\",\r\n    items: items,\r\n    total: backendOrder.total || 0,\r\n    subtotal: metadata.subtotal || backendOrder.total || 0,\r\n    discount: metadata.discount || 0,\r\n    customer: metadata.customer || {\r\n      firstName: \"\",\r\n      lastName: \"\",\r\n      email: \"\",\r\n      phone: \"\",\r\n    },\r\n    shipping: metadata.shipping || {\r\n      address: \"\",\r\n      city: \"\",\r\n      postalCode: \"\",\r\n    },\r\n    paymentMethod: metadata.paymentMethod || \"cash\",\r\n    status: (backendOrder.status as OrderStatus) || \"pending\",\r\n    createdAt: backendOrder.createdAt || new Date().toISOString(),\r\n    updatedAt: backendOrder.updatedAt,\r\n  };\r\n};\r\n\r\n/**\r\n * Create a new order\r\n */\r\nexport const createOrder = async (\r\n  orderData: Omit<Order, \"id\" | \"createdAt\">,\r\n  userId: string\r\n): Promise<Order> => {\r\n  try {\r\n    const backendOrderData = mapFrontendOrderToBackend(orderData, userId);\r\n\r\n    const response = await apiClient.post(\"/orders\", backendOrderData);\r\n\r\n    if (response.data && response.data.status === \"success\") {\r\n      return mapBackendOrderToFrontend(response.data.data.order);\r\n    } else {\r\n      throw new Error(\"Failed to create order\");\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error creating order:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get all orders for the current user\r\n */\r\nexport const getUserOrders = async (userId: string): Promise<Order[]> => {\r\n  try {\r\n    // Assuming the backend has a way to filter orders by user\r\n    // This might need to be adjusted based on the actual backend implementation\r\n    const response = await apiClient.get(`/orders?user=${userId}`);\r\n\r\n    if (response.data && response.data.status === \"success\") {\r\n      return response.data.data.orders.map(mapBackendOrderToFrontend);\r\n    } else {\r\n      return [];\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error fetching user orders:\", error);\r\n    return [];\r\n  }\r\n};\r\n\r\n/**\r\n * Get a single order by ID\r\n */\r\nexport const getOrderById = async (orderId: string): Promise<Order | null> => {\r\n  try {\r\n    const response = await apiClient.get(`/orders/${orderId}`);\r\n\r\n    if (response.data && response.data.status === \"success\") {\r\n      return mapBackendOrderToFrontend(response.data.data.order);\r\n    } else {\r\n      return null;\r\n    }\r\n  } catch (error) {\r\n    console.error(`Error fetching order with ID ${orderId}:`, error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Update order status\r\n */\r\nexport const updateOrderStatus = async (\r\n  orderId: string,\r\n  status: OrderStatus\r\n): Promise<Order | null> => {\r\n  try {\r\n    const response = await apiClient.patch(`/orders/${orderId}`, { status });\r\n\r\n    if (response.data && response.data.status === \"success\") {\r\n      return mapBackendOrderToFrontend(response.data.data.order);\r\n    } else {\r\n      return null;\r\n    }\r\n  } catch (error) {\r\n    console.error(`Error updating order status for ID ${orderId}:`, error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Cancel an order (update status to cancelled)\r\n */\r\nexport const cancelOrder = async (orderId: string): Promise<Order | null> => {\r\n  return updateOrderStatus(orderId, \"cancelled\");\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAIA,uCAAuC;AACvC,MAAM,4BAA4B,CAChC,OACA;IAEA,uBAAuB;IACvB,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,KAAK,GAAG;QAC/C,QAAQ,KAAK,CAAC,wBAAwB,MAAM,KAAK;QACjD,MAAM,IAAI,MAAM;IAClB;IAEA,+BAA+B;IAC/B,MAAM,aAAa,MAAM,KAAK,CAAC,MAAM,CACnC,CAAC,OAAS,QAAQ,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,EAAE;IAGnD,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,QAAQ,KAAK,CAAC;QACd,MAAM,IAAI,MAAM;IAClB;IAEA,uDAAuD;IACvD,MAAM,QAAQ,WAAW,GAAG,CAAC,CAAC;QAC5B,+BAA+B;QAC/B,IAAI,WAAW,mCAAmC,sBAAsB;QACxE,IAAI,KAAK,OAAO,CAAC,MAAM,IAAI,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG;YACzD,WAAW,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE;QACnC,OAAO,IAAI,KAAK,OAAO,CAAC,KAAK,EAAE;YAC7B,WAAW,KAAK,OAAO,CAAC,KAAK;QAC/B;QAEA,OAAO;YACL,WAAW,KAAK,OAAO,CAAC,EAAE;YAC1B,MAAM,KAAK,OAAO,CAAC,KAAK,IAAI,KAAK,OAAO,CAAC,IAAI,IAAI;YACjD,OAAO,KAAK,OAAO,CAAC,KAAK,IAAI;YAC7B,UAAU,KAAK,QAAQ;YACvB,OAAO;QACT;IACF;IAEA,iCAAiC;IACjC,MAAM,kBAAkB;QACtB,MAAM,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,QAAQ,EAAE;QAC9D,SAAS,MAAM,QAAQ,CAAC,OAAO;QAC/B,MAAM,MAAM,QAAQ,CAAC,IAAI;QACzB,YAAY,MAAM,QAAQ,CAAC,UAAU,IAAI;QACzC,SAAS;QACT,OAAO,MAAM,QAAQ,CAAC,KAAK;IAC7B;IAEA,OAAO;QACL,MAAM;QACN,OAAO;QACP,OAAO,MAAM,KAAK;QAClB,QAAQ,MAAM,MAAM;QACpB,iBAAiB;QACjB,eAAe,MAAM,aAAa,KAAK,SAAS,qBACjC,MAAM,aAAa,KAAK,SAAS,kBAAkB;QAClE,OAAO;QACP,2DAA2D;QAC3D,UAAU;YACR,UAAU,MAAM,QAAQ;YACxB,UAAU,MAAM,QAAQ;YACxB,eAAe,MAAM,aAAa;YAClC,UAAU,MAAM,QAAQ;YACxB,UAAU,MAAM,QAAQ;QAC1B;IACF;AACF;AAEA,uCAAuC;AACvC,MAAM,4BAA4B,CAAC;IACjC,IAAI,CAAC,cAAc;QACjB,QAAQ,KAAK,CAAC;QACd,OAAO;YACL,IAAI;YACJ,OAAO,EAAE;YACT,OAAO;YACP,UAAU;YACV,UAAU;YACV,UAAU;gBACR,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,OAAO;YACT;YACA,UAAU;gBACR,SAAS;gBACT,MAAM;gBACN,YAAY;YACd;YACA,eAAe;YACf,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,sDAAsD;IACtD,MAAM,WAAW,aAAa,QAAQ,IAAI,CAAC;IAE3C,mCAAmC;IACnC,QAAQ,GAAG,CAAC,uBAAuB;IACnC,QAAQ,GAAG,CAAC,aAAa;IAEzB,iFAAiF;IACjF,IAAI,QAAQ,EAAE;IAEd,8DAA8D;IAC9D,IAAI,aAAa,KAAK,IAAI,MAAM,OAAO,CAAC,aAAa,KAAK,GAAG;QAC3D,QAAQ,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;gBAC7C,SAAS;oBACP,IAAI,KAAK,SAAS,IAAI;oBACtB,OAAO,KAAK,IAAI,IAAI;oBACpB,OAAO,KAAK,KAAK,IAAI;oBACrB,QAAQ,KAAK,KAAK,GAAG;wBAAC,KAAK,KAAK;qBAAC,GAAG,EAAE;oBACtC,OAAO,KAAK,KAAK,IAAI;gBAAS;gBAChC,UAAU,KAAK,QAAQ,IAAI;YAC7B,CAAC;IACH,OAEK,IAAI,SAAS,KAAK,IAAI,MAAM,OAAO,CAAC,SAAS,KAAK,GAAG;QACxD,4CAA4C;QAC5C,QAAQ,SAAS,KAAK,CACnB,MAAM,CAAC,CAAC,OAAa,QAAQ,KAAK,OAAO,EACzC,GAAG,CAAC,CAAC,OAAa,CAAC;gBAClB,SAAS;oBACP,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,KAAK,SAAS,IAAI;oBACzC,OAAO,KAAK,OAAO,CAAC,KAAK,IAAI;oBAC7B,OAAO,KAAK,OAAO,CAAC,KAAK,IAAI;oBAC7B,QAAQ,KAAK,OAAO,CAAC,MAAM,IAAI,EAAE;oBACjC,OAAO,KAAK,OAAO,CAAC,KAAK,IAAI;gBAC/B;gBACA,UAAU,KAAK,QAAQ,IAAI;YAC7B,CAAC;IACL,OAEK,IACH,aAAa,QAAQ,IACrB,MAAM,OAAO,CAAC,aAAa,QAAQ,GACnC;QACA,iDAAiD;QACjD,aAAa,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC7B,IAAI,SAAS;gBACX,MAAM,IAAI,CAAC;oBACT,SAAS;wBACP,IAAI,QAAQ,GAAG,IAAI;wBACnB,OAAO,QAAQ,IAAI,IAAI;wBACvB,OAAO,QAAQ,KAAK,IAAI;wBACxB,QAAQ,QAAQ,MAAM,IAAI,CAAC,QAAQ,KAAK,GAAG;4BAAC,QAAQ,KAAK;yBAAC,GAAG,EAAE;wBAC/D,OAAO,QAAQ,KAAK,IAAI;oBAC1B;oBACA,UAAU;gBACZ;YACF;QACF;IACF;IAEA,OAAO;QACL,IAAI,aAAa,GAAG,IAAI;QACxB,OAAO;QACP,OAAO,aAAa,KAAK,IAAI;QAC7B,UAAU,SAAS,QAAQ,IAAI,aAAa,KAAK,IAAI;QACrD,UAAU,SAAS,QAAQ,IAAI;QAC/B,UAAU,SAAS,QAAQ,IAAI;YAC7B,WAAW;YACX,UAAU;YACV,OAAO;YACP,OAAO;QACT;QACA,UAAU,SAAS,QAAQ,IAAI;YAC7B,SAAS;YACT,MAAM;YACN,YAAY;QACd;QACA,eAAe,SAAS,aAAa,IAAI;QACzC,QAAQ,AAAC,aAAa,MAAM,IAAoB;QAChD,WAAW,aAAa,SAAS,IAAI,IAAI,OAAO,WAAW;QAC3D,WAAW,aAAa,SAAS;IACnC;AACF;AAKO,MAAM,cAAc,OACzB,WACA;IAEA,IAAI;QACF,MAAM,mBAAmB,0BAA0B,WAAW;QAE9D,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,WAAW;QAEjD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,WAAW;YACvD,OAAO,0BAA0B,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;QAC3D,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAKO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,0DAA0D;QAC1D,4EAA4E;QAC5E,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ;QAE7D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,WAAW;YACvD,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QACvC,OAAO;YACL,OAAO,EAAE;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,EAAE;IACX;AACF;AAKO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,SAAS;QAEzD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,WAAW;YACvD,OAAO,0BAA0B,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;QAC3D,OAAO;YACL,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,QAAQ,CAAC,CAAC,EAAE;QAC1D,OAAO;IACT;AACF;AAKO,MAAM,oBAAoB,OAC/B,SACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,uHAAA,CAAA,UAAS,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE;YAAE;QAAO;QAEtE,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,WAAW;YACvD,OAAO,0BAA0B,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;QAC3D,OAAO;YACL,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,QAAQ,CAAC,CAAC,EAAE;QAChE,OAAO;IACT;AACF;AAKO,MAAM,cAAc,OAAO;IAChC,OAAO,kBAAkB,SAAS;AACpC", "debugId": null}}, {"offset": {"line": 3357, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/contexts/OrderContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, {\r\n  createContext,\r\n  useContext,\r\n  useState,\r\n  useEffect,\r\n  ReactNode,\r\n} from \"react\";\r\nimport { Order, OrderContextType, OrderStatus } from \"@/types/order\";\r\nimport { toast } from \"sonner\";\r\nimport { useAuth } from \"./AuthContext\";\r\nimport * as orderApi from \"@/apis/orders\";\r\n\r\n// Create context\r\nconst OrderContext = createContext<OrderContextType | undefined>(undefined);\r\n\r\n// Provider component\r\nexport const OrderProvider = ({ children }: { children: ReactNode }) => {\r\n  const [orders, setOrders] = useState<Order[]>([]);\r\n  const [isLoading, setIsLoading] = useState<boolean>(false);\r\n  const { user, isAuthenticated } = useAuth();\r\n\r\n  // Fetch orders from API when user is authenticated\r\n  useEffect(() => {\r\n    const fetchOrders = async () => {\r\n      if (isAuthenticated && user?.id) {\r\n        setIsLoading(true);\r\n        try {\r\n          const userOrders = await orderApi.getUserOrders(user.id);\r\n          setOrders(userOrders);\r\n        } catch (error) {\r\n          console.error(\"Failed to fetch orders:\", error);\r\n          toast.error(\"Failed to load your orders. Please try again later.\");\r\n        } finally {\r\n          setIsLoading(false);\r\n        }\r\n      } else {\r\n        // Clear orders when user is not authenticated\r\n        setOrders([]);\r\n      }\r\n    };\r\n\r\n    fetchOrders();\r\n  }, [isAuthenticated, user]);\r\n\r\n  // Get a specific order by ID\r\n  const getOrder = async (id: string): Promise<Order | undefined> => {\r\n    // First check if the order is already in state\r\n    const cachedOrder = orders.find((order) => order.id === id);\r\n    if (cachedOrder) return cachedOrder;\r\n\r\n    // If not found in state, try to fetch from API\r\n    if (isAuthenticated) {\r\n      try {\r\n        const order = await orderApi.getOrderById(id);\r\n        return order || undefined;\r\n      } catch (error) {\r\n        console.error(`Failed to fetch order ${id}:`, error);\r\n        return undefined;\r\n      }\r\n    }\r\n\r\n    return undefined;\r\n  };\r\n\r\n  // Create a new order\r\n  const createOrder = async (\r\n    orderData: Omit<Order, \"id\" | \"createdAt\">\r\n  ): Promise<Order> => {\r\n    if (!isAuthenticated || !user?.id) {\r\n      throw new Error(\"You must be logged in to create an order\");\r\n    }\r\n\r\n    try {\r\n      const newOrder = await orderApi.createOrder(orderData, user.id);\r\n\r\n      // Update local state with the new order\r\n      setOrders((prevOrders) => [...prevOrders, newOrder]);\r\n\r\n      toast.success(\"Order created successfully!\");\r\n      return newOrder;\r\n    } catch (error) {\r\n      console.error(\"Failed to create order:\", error);\r\n      toast.error(\"Failed to create order. Please try again.\");\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Update order status\r\n  const updateOrderStatus = async (\r\n    id: string,\r\n    status: OrderStatus\r\n  ): Promise<void> => {\r\n    if (!isAuthenticated) {\r\n      throw new Error(\"You must be logged in to update an order\");\r\n    }\r\n\r\n    try {\r\n      const updatedOrder = await orderApi.updateOrderStatus(id, status);\r\n\r\n      if (updatedOrder) {\r\n        // Update local state\r\n        setOrders((prevOrders) =>\r\n          prevOrders.map((order) => (order.id === id ? updatedOrder : order))\r\n        );\r\n\r\n        toast.success(`Order status updated to ${status}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(`Failed to update order ${id} status:`, error);\r\n      toast.error(\"Failed to update order status. Please try again.\");\r\n    }\r\n  };\r\n\r\n  // Cancel an order\r\n  const cancelOrder = async (id: string): Promise<void> => {\r\n    if (!isAuthenticated) {\r\n      throw new Error(\"You must be logged in to cancel an order\");\r\n    }\r\n\r\n    try {\r\n      const cancelledOrder = await orderApi.cancelOrder(id);\r\n\r\n      if (cancelledOrder) {\r\n        // Update local state\r\n        setOrders((prevOrders) =>\r\n          prevOrders.map((order) => (order.id === id ? cancelledOrder : order))\r\n        );\r\n\r\n        toast.info(\"Order cancelled\");\r\n      }\r\n    } catch (error) {\r\n      console.error(`Failed to cancel order ${id}:`, error);\r\n      toast.error(\"Failed to cancel order. Please try again.\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <OrderContext.Provider\r\n      value={{\r\n        orders,\r\n        isLoading,\r\n        getOrder,\r\n        createOrder,\r\n        updateOrderStatus,\r\n        cancelOrder,\r\n      }}\r\n    >\r\n      {children}\r\n    </OrderContext.Provider>\r\n  );\r\n};\r\n\r\n// Custom hook to use order context\r\nexport const useOrders = (): OrderContextType => {\r\n  const context = useContext(OrderContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useOrders must be used within an OrderProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAQA;AACA;AACA;AAZA;;;;;;AAcA,iBAAiB;AACjB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAG1D,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAA2B;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAExC,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI,mBAAmB,MAAM,IAAI;gBAC/B,aAAa;gBACb,IAAI;oBACF,MAAM,aAAa,MAAM,CAAA,GAAA,8GAAA,CAAA,gBAAsB,AAAD,EAAE,KAAK,EAAE;oBACvD,UAAU;gBACZ,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2BAA2B;oBACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,SAAU;oBACR,aAAa;gBACf;YACF,OAAO;gBACL,8CAA8C;gBAC9C,UAAU,EAAE;YACd;QACF;QAEA;IACF,GAAG;QAAC;QAAiB;KAAK;IAE1B,6BAA6B;IAC7B,MAAM,WAAW,OAAO;QACtB,+CAA+C;QAC/C,MAAM,cAAc,OAAO,IAAI,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;QACxD,IAAI,aAAa,OAAO;QAExB,+CAA+C;QAC/C,IAAI,iBAAiB;YACnB,IAAI;gBACF,MAAM,QAAQ,MAAM,CAAA,GAAA,8GAAA,CAAA,eAAqB,AAAD,EAAE;gBAC1C,OAAO,SAAS;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;gBAC9C,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,qBAAqB;IACrB,MAAM,cAAc,OAClB;QAEA,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI;YACjC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8GAAA,CAAA,cAAoB,AAAD,EAAE,WAAW,KAAK,EAAE;YAE9D,wCAAwC;YACxC,UAAU,CAAC,aAAe;uBAAI;oBAAY;iBAAS;YAEnD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,OACxB,IACA;QAEA,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,eAAe,MAAM,CAAA,GAAA,8GAAA,CAAA,oBAA0B,AAAD,EAAE,IAAI;YAE1D,IAAI,cAAc;gBAChB,qBAAqB;gBACrB,UAAU,CAAC,aACT,WAAW,GAAG,CAAC,CAAC,QAAW,MAAM,EAAE,KAAK,KAAK,eAAe;gBAG9D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,wBAAwB,EAAE,QAAQ;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,kBAAkB;IAClB,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,8GAAA,CAAA,cAAoB,AAAD,EAAE;YAElD,IAAI,gBAAgB;gBAClB,qBAAqB;gBACrB,UAAU,CAAC,aACT,WAAW,GAAG,CAAC,CAAC,QAAW,MAAM,EAAE,KAAK,KAAK,iBAAiB;gBAGhE,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QACpB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAGO,MAAM,YAAY;IACvB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3502, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/contexts/ModalContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { createContext, useContext, useState, ReactNode } from \"react\";\r\n\r\ninterface ModalContextType {\r\n  isAnyModalOpen: boolean;\r\n  openModal: (modalId: string) => void;\r\n  closeModal: (modalId: string) => void;\r\n}\r\n\r\nconst ModalContext = createContext<ModalContextType | undefined>(undefined);\r\n\r\nexport const useModal = () => {\r\n  const context = useContext(ModalContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useModal must be used within a ModalProvider\");\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface ModalProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const ModalProvider: React.FC<ModalProviderProps> = ({ children }) => {\r\n  const [openModals, setOpenModals] = useState<Set<string>>(new Set());\r\n\r\n  const openModal = React.useCallback((modalId: string) => {\r\n    setOpenModals((prev) => {\r\n      if (prev.has(modalId)) return prev;\r\n      const newSet = new Set(prev);\r\n      newSet.add(modalId);\r\n      return newSet;\r\n    });\r\n  }, []);\r\n\r\n  const closeModal = React.useCallback((modalId: string) => {\r\n    setOpenModals((prev) => {\r\n      if (!prev.has(modalId)) return prev;\r\n      const newSet = new Set(prev);\r\n      newSet.delete(modalId);\r\n      return newSet;\r\n    });\r\n  }, []);\r\n\r\n  const isAnyModalOpen = openModals.size > 0;\r\n\r\n  return (\r\n    <ModalContext.Provider value={{ isAnyModalOpen, openModal, closeModal }}>\r\n      {children}\r\n    </ModalContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AASA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAE9D,MAAM,YAAY,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC;QACnC,cAAc,CAAC;YACb,IAAI,KAAK,GAAG,CAAC,UAAU,OAAO;YAC9B,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,GAAG,CAAC;YACX,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC;QACpC,cAAc,CAAC;YACb,IAAI,CAAC,KAAK,GAAG,CAAC,UAAU,OAAO;YAC/B,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,MAAM,CAAC;YACd,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,WAAW,IAAI,GAAG;IAEzC,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAgB;YAAW;QAAW;kBACnE;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 3557, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/lib/termsAcceptance.ts"], "sourcesContent": ["/**\n * Terms of Service Acceptance Utilities\n * \n * This module handles the storage and retrieval of terms acceptance data\n * in localStorage with proper error handling and type safety.\n */\n\nexport interface TermsAcceptanceData {\n  accepted: boolean;\n  timestamp: string;\n  version: string;\n  userAgent?: string;\n}\n\n// Current version of terms - increment when terms are updated\nexport const CURRENT_TERMS_VERSION = \"1.0.0\";\n\n// localStorage key for terms acceptance\nconst TERMS_ACCEPTANCE_KEY = \"chinioti_terms_acceptance\";\n\n/**\n * Check if user has accepted the current version of terms\n */\nexport function hasAcceptedTerms(): boolean {\n  try {\n    const stored = localStorage.getItem(TERMS_ACCEPTANCE_KEY);\n    if (!stored) return false;\n\n    const data: TermsAcceptanceData = JSON.parse(stored);\n    \n    // Check if user accepted the current version\n    return data.accepted && data.version === CURRENT_TERMS_VERSION;\n  } catch (error) {\n    console.error(\"Error checking terms acceptance:\", error);\n    return false;\n  }\n}\n\n/**\n * Record user's acceptance of terms\n */\nexport function acceptTerms(): void {\n  try {\n    const acceptanceData: TermsAcceptanceData = {\n      accepted: true,\n      timestamp: new Date().toISOString(),\n      version: CURRENT_TERMS_VERSION,\n      userAgent: typeof window !== \"undefined\" ? window.navigator.userAgent : undefined,\n    };\n\n    localStorage.setItem(TERMS_ACCEPTANCE_KEY, JSON.stringify(acceptanceData));\n  } catch (error) {\n    console.error(\"Error storing terms acceptance:\", error);\n  }\n}\n\n/**\n * Get terms acceptance data\n */\nexport function getTermsAcceptanceData(): TermsAcceptanceData | null {\n  try {\n    const stored = localStorage.getItem(TERMS_ACCEPTANCE_KEY);\n    if (!stored) return null;\n\n    return JSON.parse(stored);\n  } catch (error) {\n    console.error(\"Error retrieving terms acceptance data:\", error);\n    return null;\n  }\n}\n\n/**\n * Clear terms acceptance (for testing or when terms are updated)\n */\nexport function clearTermsAcceptance(): void {\n  try {\n    localStorage.removeItem(TERMS_ACCEPTANCE_KEY);\n  } catch (error) {\n    console.error(\"Error clearing terms acceptance:\", error);\n  }\n}\n\n/**\n * Check if we should show the terms modal\n * This function can be extended with additional logic like:\n * - Checking if user is on certain pages\n * - Checking user authentication status\n * - Checking if enough time has passed since last check\n */\nexport function shouldShowTermsModal(): boolean {\n  // Don't show on server side\n  if (typeof window === \"undefined\") return false;\n  \n  // Don't show if already accepted current version\n  if (hasAcceptedTerms()) return false;\n  \n  // Add any additional conditions here\n  return true;\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;AAUM,MAAM,wBAAwB;AAErC,wCAAwC;AACxC,MAAM,uBAAuB;AAKtB,SAAS;IACd,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,CAAC,QAAQ,OAAO;QAEpB,MAAM,OAA4B,KAAK,KAAK,CAAC;QAE7C,6CAA6C;QAC7C,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAKO,SAAS;IACd,IAAI;QACF,MAAM,iBAAsC;YAC1C,UAAU;YACV,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS;YACT,WAAW,6EAA6D;QAC1E;QAEA,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;IACnD;AACF;AAKO,SAAS;IACd,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,CAAC,QAAQ,OAAO;QAEpB,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;IACT;AACF;AAKO,SAAS;IACd,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;IACpD;AACF;AASO,SAAS;IACd,4BAA4B;IAC5B,wCAAmC,OAAO;;AAO5C", "debugId": null}}, {"offset": {"line": 3626, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/contexts/TermsAcceptanceContext.tsx"], "sourcesContent": ["'use client';\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { \n  hasAcceptedTerms, \n  acceptTerms, \n  shouldShowTermsModal,\n  getTermsAcceptanceData,\n  TermsAcceptanceData \n} from '@/lib/termsAcceptance';\n\ninterface TermsAcceptanceContextType {\n  showModal: boolean;\n  acceptanceData: TermsAcceptanceData | null;\n  handleAcceptTerms: () => void;\n  handleDeclineTerms: () => void;\n  isLoading: boolean;\n}\n\nconst TermsAcceptanceContext = createContext<TermsAcceptanceContextType | undefined>(undefined);\n\nexport const useTermsAcceptance = () => {\n  const context = useContext(TermsAcceptanceContext);\n  if (context === undefined) {\n    throw new Error('useTermsAcceptance must be used within a TermsAcceptanceProvider');\n  }\n  return context;\n};\n\ninterface TermsAcceptanceProviderProps {\n  children: ReactNode;\n}\n\nexport const TermsAcceptanceProvider: React.FC<TermsAcceptanceProviderProps> = ({ children }) => {\n  const [showModal, setShowModal] = useState(false);\n  const [acceptanceData, setAcceptanceData] = useState<TermsAcceptanceData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Check terms acceptance status on mount\n  useEffect(() => {\n    // Small delay to ensure proper hydration\n    const timer = setTimeout(() => {\n      const shouldShow = shouldShowTermsModal();\n      const data = getTermsAcceptanceData();\n      \n      setShowModal(shouldShow);\n      setAcceptanceData(data);\n      setIsLoading(false);\n    }, 100);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const handleAcceptTerms = () => {\n    try {\n      acceptTerms();\n      const updatedData = getTermsAcceptanceData();\n      setAcceptanceData(updatedData);\n      setShowModal(false);\n    } catch (error) {\n      console.error('Error accepting terms:', error);\n    }\n  };\n\n  const handleDeclineTerms = () => {\n    // For now, just close the modal\n    // In a more strict implementation, you might redirect the user away\n    // or show a message explaining that they cannot use the site without accepting\n    setShowModal(false);\n    \n    // Show the modal again after a short delay to encourage acceptance\n    setTimeout(() => {\n      if (!hasAcceptedTerms()) {\n        setShowModal(true);\n      }\n    }, 5000); // Show again after 5 seconds\n  };\n\n  const value: TermsAcceptanceContextType = {\n    showModal,\n    acceptanceData,\n    handleAcceptTerms,\n    handleDeclineTerms,\n    isLoading,\n  };\n\n  return (\n    <TermsAcceptanceContext.Provider value={value}>\n      {children}\n    </TermsAcceptanceContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAFA;;;;AAkBA,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA0C;AAE9E,MAAM,qBAAqB;IAChC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,0BAAkE,CAAC,EAAE,QAAQ,EAAE;IAC1F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IACjF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yCAAyC;QACzC,MAAM,QAAQ,WAAW;YACvB,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;YACtC,MAAM,OAAO,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;YAElC,aAAa;YACb,kBAAkB;YAClB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;YACV,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;YACzC,kBAAkB;YAClB,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,qBAAqB;QACzB,gCAAgC;QAChC,oEAAoE;QACpE,+EAA+E;QAC/E,aAAa;QAEb,mEAAmE;QACnE,WAAW;YACT,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,KAAK;gBACvB,aAAa;YACf;QACF,GAAG,OAAO,6BAA6B;IACzC;IAEA,MAAM,QAAoC;QACxC;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,uBAAuB,QAAQ;QAAC,OAAO;kBACrC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 3705, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/WhatsAppButton.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { FaWhatsapp } from 'react-icons/fa';\r\nimport { useModal } from '@/contexts/ModalContext';\r\n\r\ninterface WhatsAppButtonProps {\r\n  phoneNumber: string;\r\n  message?: string;\r\n}\r\n\r\nconst WhatsAppButton: React.FC<WhatsAppButtonProps> = ({\r\n  phoneNumber,\r\n  message = 'Hello! I have a question about your products.',\r\n}) => {\r\n  const { isAnyModalOpen } = useModal();\r\n  // Format the phone number to ensure it works with WhatsApp\r\n  const formattedPhoneNumber = phoneNumber.startsWith('+')\r\n    ? phoneNumber.substring(1)\r\n    : phoneNumber.startsWith('0')\r\n    ? `92${phoneNumber.substring(1)}`\r\n    : phoneNumber;\r\n\r\n  // Create the WhatsApp URL\r\n  const whatsappUrl = `https://wa.me/${formattedPhoneNumber}?text=${encodeURIComponent(\r\n    message\r\n  )}`;\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {!isAnyModalOpen && (\r\n        <motion.div\r\n          className=\"fixed bottom-6 right-6 z-40 flex flex-col items-end gap-2\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: 20 }}\r\n          transition={{ type: 'spring', stiffness: 300, damping: 20 }}\r\n        >\r\n      {/* Tooltip - Only visible on hover on desktop */}\r\n      <motion.div\r\n        className=\"hidden md:block bg-white text-gray-800 text-sm py-1 px-3 rounded-lg shadow-md mb-1\"\r\n        initial={{ opacity: 0, scale: 0.8, y: 10 }}\r\n        whileHover={{ opacity: 1, scale: 1, y: 0 }}\r\n        transition={{ duration: 0.2 }}\r\n      >\r\n        Chat with us on WhatsApp\r\n      </motion.div>\r\n\r\n      {/* WhatsApp Button */}\r\n      <motion.a\r\n        href={whatsappUrl}\r\n        target=\"_blank\"\r\n        rel=\"noopener noreferrer\"\r\n        className=\"bg-green-500 text-white p-3 rounded-full shadow-lg hover:bg-green-600 transition-colors flex items-center justify-center\"\r\n        whileHover={{ scale: 1.1 }}\r\n        whileTap={{ scale: 0.9 }}\r\n        aria-label=\"Chat on WhatsApp\"\r\n      >\r\n        <FaWhatsapp size={28} />\r\n      </motion.a>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n};\r\n\r\nexport default WhatsAppButton;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAWA,MAAM,iBAAgD,CAAC,EACrD,WAAW,EACX,UAAU,+CAA+C,EAC1D;IACC,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,WAAQ,AAAD;IAClC,2DAA2D;IAC3D,MAAM,uBAAuB,YAAY,UAAU,CAAC,OAChD,YAAY,SAAS,CAAC,KACtB,YAAY,UAAU,CAAC,OACvB,CAAC,EAAE,EAAE,YAAY,SAAS,CAAC,IAAI,GAC/B;IAEJ,0BAA0B;IAC1B,MAAM,cAAc,CAAC,cAAc,EAAE,qBAAqB,MAAM,EAAE,mBAChE,UACC;IAEH,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,CAAC,gCACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC1B,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;;8BAG9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,YAAY;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACzC,YAAY;wBAAE,UAAU;oBAAI;8BAC7B;;;;;;8BAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,MAAM;oBACN,QAAO;oBACP,KAAI;oBACJ,WAAU;oBACV,YAAY;wBAAE,OAAO;oBAAI;oBACzB,UAAU;wBAAE,OAAO;oBAAI;oBACvB,cAAW;8BAEX,cAAA,8OAAC,8IAAA,CAAA,aAAU;wBAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;AAM1B;uCAEe", "debugId": null}}, {"offset": {"line": 3809, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/BackToTopButton.tsx"], "sourcesContent": ["'use client';\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ChevronUp } from 'lucide-react';\nimport { useModal } from '@/contexts/ModalContext';\n\nconst BackToTopButton = () => {\n  const [isVisible, setIsVisible] = useState(false);\n  const { isAnyModalOpen } = useModal();\n\n  // Show button when page is scrolled down\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.scrollY > 500) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n      }\n    };\n\n    window.addEventListener('scroll', toggleVisibility);\n\n    return () => window.removeEventListener('scroll', toggleVisibility);\n  }, []);\n\n  // Scroll to top function\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth',\n    });\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && !isAnyModalOpen && (\n        <motion.button\n          className=\"fixed bottom-24 right-6 p-3 bg-accent text-white rounded-full shadow-lg hover:bg-accent/90 z-50\"\n          onClick={scrollToTop}\n          initial={{ opacity: 0, scale: 0.5 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.5 }}\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.9 }}\n          transition={{ duration: 0.2 }}\n          aria-label=\"Back to top\"\n        >\n          <ChevronUp size={24} />\n        </motion.button>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default BackToTopButton;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,kBAAkB;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,WAAQ,AAAD;IAElC,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,OAAO,OAAO,GAAG,KAAK;gBACxB,aAAa;YACf,OAAO;gBACL,aAAa;YACf;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,aAAa,CAAC,gCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;YACZ,WAAU;YACV,SAAS;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,OAAO;YAAI;YACzB,UAAU;gBAAE,OAAO;YAAI;YACvB,YAAY;gBAAE,UAAU;YAAI;YAC5B,cAAW;sBAEX,cAAA,8OAAC,gNAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;;;;;;;;;;;AAK3B;uCAEe", "debugId": null}}, {"offset": {"line": 3897, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface DialogProps {\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  children: React.ReactNode\n}\n\ninterface DialogContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\ninterface DialogHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\ninterface DialogTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {\n  children: React.ReactNode\n}\n\ninterface DialogDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {\n  children: React.ReactNode\n}\n\ninterface DialogFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Dialog: React.FC<DialogProps> = ({ open, onOpenChange, children }) => {\n  React.useEffect(() => {\n    if (open) {\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset'\n    }\n  }, [open])\n\n  if (!open) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      <div \n        className=\"fixed inset-0 bg-black/50\" \n        onClick={() => onOpenChange?.(false)}\n      />\n      <div className=\"relative z-50 w-full max-w-lg mx-4\">\n        {children}\n      </div>\n    </div>\n  )\n}\n\nconst DialogContent = React.forwardRef<HTMLDivElement, DialogContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        \"bg-white rounded-lg shadow-xl p-6 w-full max-h-[90vh] overflow-y-auto\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nDialogContent.displayName = \"DialogContent\"\n\nconst DialogHeader = React.forwardRef<HTMLDivElement, DialogHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"flex flex-col space-y-2 text-center sm:text-left mb-4\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogTitle = React.forwardRef<HTMLHeadingElement, DialogTitleProps>(\n  ({ className, children, ...props }, ref) => (\n    <h2\n      ref={ref}\n      className={cn(\"text-lg font-semibold text-gray-900\", className)}\n      {...props}\n    >\n      {children}\n    </h2>\n  )\n)\nDialogTitle.displayName = \"DialogTitle\"\n\nconst DialogDescription = React.forwardRef<HTMLParagraphElement, DialogDescriptionProps>(\n  ({ className, children, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn(\"text-sm text-gray-600\", className)}\n      {...props}\n    >\n      {children}\n    </p>\n  )\n)\nDialogDescription.displayName = \"DialogDescription\"\n\nconst DialogFooter = React.forwardRef<HTMLDivElement, DialogFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 mt-6\", className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nexport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogDescription,\n  DialogFooter,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AAEA;AAJA;;;;AAgCA,MAAM,SAAgC,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE;IACrE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,MAAM;YACR,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAK;IAET,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAEhC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;AAEA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yEACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,cAAc,WAAW,GAAG;AAE5B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;kBAER;;;;;;AAIP,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;QACpD,GAAG,KAAK;kBAER;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACvC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAIP,kBAAkB,WAAW,GAAG;AAEhC,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sEAAsE;QACnF,GAAG,KAAK;kBAER;;;;;;AAIP,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4014, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/TermsAcceptanceModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogDescription,\r\n  DialogFooter,\r\n} from \"@/components/ui/dialog\";\r\nimport { useTermsAcceptance } from \"@/contexts/TermsAcceptanceContext\";\r\nimport { useModal } from \"@/contexts/ModalContext\";\r\nimport { ExternalLink, Shield, FileText, Eye } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\n\r\nconst TermsAcceptanceModal = () => {\r\n  const { showModal, handleAcceptTerms, handleDeclineTerms } =\r\n    useTermsAcceptance();\r\n  const { openModal, closeModal } = useModal();\r\n  const [showFullTerms, setShowFullTerms] = useState(false);\r\n\r\n  // Register this modal with the modal context\r\n  React.useEffect(() => {\r\n    if (showModal) {\r\n      openModal(\"terms-acceptance\");\r\n    } else if (!showModal) {\r\n      closeModal(\"terms-acceptance\");\r\n    }\r\n  }, [showModal]); // Only include showModal in the dependency array\r\n\r\n  const handleAccept = () => {\r\n    handleAcceptTerms();\r\n    closeModal(\"terms-acceptance\");\r\n  };\r\n\r\n  const handleDecline = () => {\r\n    handleDeclineTerms();\r\n    closeModal(\"terms-acceptance\");\r\n  };\r\n\r\n  const toggleFullTerms = () => {\r\n    setShowFullTerms(!showFullTerms);\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {showModal && (\r\n        <Dialog open={showModal} onOpenChange={() => {}}>\r\n          <DialogContent className=\"max-w-2xl\">\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.95 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              exit={{ opacity: 0, scale: 0.95 }}\r\n              transition={{ duration: 0.2 }}\r\n            >\r\n              <DialogHeader>\r\n                <div className=\"flex items-center gap-3 mb-2\">\r\n                  <div className=\"p-2 bg-accent/10 rounded-full\">\r\n                    <Shield className=\"w-6 h-6 text-accent\" />\r\n                  </div>\r\n                  <DialogTitle className=\"text-xl\">\r\n                    Welcome to Chinioti Wooden Art\r\n                  </DialogTitle>\r\n                </div>\r\n                <DialogDescription className=\"text-left\">\r\n                  Before you continue, please review and accept our Terms of\r\n                  Service and Privacy Policy to ensure the best experience on\r\n                  our platform.\r\n                </DialogDescription>\r\n              </DialogHeader>\r\n\r\n              <div className=\"my-6\">\r\n                {!showFullTerms ? (\r\n                  <div className=\"space-y-4\">\r\n                    <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                      <h3 className=\"font-semibold text-gray-900 mb-2\">\r\n                        Quick Summary:\r\n                      </h3>\r\n                      <ul className=\"text-sm text-gray-700 space-y-1\">\r\n                        <li>\r\n                          • We respect your privacy and protect your personal\r\n                          information\r\n                        </li>\r\n                        <li>\r\n                          • Our products are handcrafted with traditional\r\n                          Chinioti craftsmanship\r\n                        </li>\r\n                        <li>\r\n                          • We provide secure payment processing and reliable\r\n                          delivery\r\n                        </li>\r\n                        <li>• Customer satisfaction is our top priority</li>\r\n                      </ul>\r\n                    </div>\r\n\r\n                    <div className=\"flex flex-wrap gap-2\">\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={toggleFullTerms}\r\n                        className=\"flex items-center gap-2\"\r\n                      >\r\n                        <Eye className=\"w-4 h-4\" />\r\n                        Read Full Terms\r\n                      </Button>\r\n                      <Link href=\"/terms-of-service\" target=\"_blank\">\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"sm\"\r\n                          className=\"flex items-center gap-2\"\r\n                        >\r\n                          <FileText className=\"w-4 h-4\" />\r\n                          Terms of Service\r\n                          <ExternalLink className=\"w-3 h-3\" />\r\n                        </Button>\r\n                      </Link>\r\n                      <Link href=\"/privacy-policy\" target=\"_blank\">\r\n                        <Button\r\n                          variant=\"outline\"\r\n                          size=\"sm\"\r\n                          className=\"flex items-center gap-2\"\r\n                        >\r\n                          <Shield className=\"w-4 h-4\" />\r\n                          Privacy Policy\r\n                          <ExternalLink className=\"w-3 h-3\" />\r\n                        </Button>\r\n                      </Link>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"space-y-4\">\r\n                    <div className=\"max-h-60 overflow-y-auto bg-gray-50 p-4 rounded-lg text-sm\">\r\n                      <h3 className=\"font-semibold mb-2\">\r\n                        Terms of Service - Key Points:\r\n                      </h3>\r\n                      <div className=\"space-y-2 text-gray-700\">\r\n                        <p>\r\n                          <strong>1. Product Quality:</strong> All our furniture\r\n                          is handcrafted using traditional Chinioti techniques\r\n                          with premium wood materials.\r\n                        </p>\r\n                        <p>\r\n                          <strong>2. Orders & Payment:</strong> We accept secure\r\n                          online payments and process orders within 1-2 business\r\n                          days.\r\n                        </p>\r\n                        <p>\r\n                          <strong>3. Delivery:</strong> We provide reliable\r\n                          delivery across Pakistan with tracking information.\r\n                        </p>\r\n                        <p>\r\n                          <strong>4. Returns:</strong> 7-day return policy for\r\n                          damaged or defective items.\r\n                        </p>\r\n                        <p>\r\n                          <strong>5. Privacy:</strong> We protect your personal\r\n                          information and never share it with third parties\r\n                          without consent.\r\n                        </p>\r\n                        <p>\r\n                          <strong>6. Custom Orders:</strong> We accept custom\r\n                          furniture requests with detailed specifications.\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={toggleFullTerms}\r\n                      className=\"flex items-center gap-2\"\r\n                    >\r\n                      Show Summary\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              <DialogFooter>\r\n                <div className=\"flex flex-col sm:flex-row gap-3 w-full\">\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    onClick={handleDecline}\r\n                    className=\"flex-1 sm:flex-none\"\r\n                  >\r\n                    I'll Review Later\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleAccept}\r\n                    className=\"flex-1 sm:flex-none bg-[#000000] text-white\"\r\n                  >\r\n                    I Accept Terms & Privacy Policy\r\n                  </Button>\r\n                </div>\r\n              </DialogFooter>\r\n\r\n              <div className=\"mt-4 text-xs text-gray-500 text-center\">\r\n                By accepting, you agree to our Terms of Service and Privacy\r\n                Policy. You can review these documents anytime in our footer.\r\n              </div>\r\n            </motion.div>\r\n          </DialogContent>\r\n        </Dialog>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n};\r\n\r\nexport default TermsAcceptanceModal;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAfA;;;;;;;;;;AAiBA,MAAM,uBAAuB;IAC3B,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,GACxD,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;IACnB,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,WAAQ,AAAD;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,WAAW;YACb,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW;YACrB,WAAW;QACb;IACF,GAAG;QAAC;KAAU,GAAG,iDAAiD;IAElE,MAAM,eAAe;QACnB;QACA,WAAW;IACb;IAEA,MAAM,gBAAgB;QACpB;QACA,WAAW;IACb;IAEA,MAAM,kBAAkB;QACtB,iBAAiB,CAAC;IACpB;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,2HAAA,CAAA,SAAM;YAAC,MAAM;YAAW,cAAc,KAAO;sBAC5C,cAAA,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAK;oBACnC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAK;oBAChC,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC,2HAAA,CAAA,eAAY;;8CACX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC,2HAAA,CAAA,cAAW;4CAAC,WAAU;sDAAU;;;;;;;;;;;;8CAInC,8OAAC,2HAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAAY;;;;;;;;;;;;sCAO3C,8OAAC;4BAAI,WAAU;sCACZ,CAAC,8BACA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DAGjD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEAIJ,8OAAC;kEAAG;;;;;;kEAIJ,8OAAC;kEAAG;;;;;;kEAIJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG7B,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,QAAO;0DACpC,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;sEAEhC,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG5B,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkB,QAAO;0DAClC,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;sEAE9B,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAMhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DAGnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;0EAAO;;;;;;4DAA4B;;;;;;;kEAItC,8OAAC;;0EACC,8OAAC;0EAAO;;;;;;4DAA6B;;;;;;;kEAIvC,8OAAC;;0EACC,8OAAC;0EAAO;;;;;;4DAAqB;;;;;;;kEAG/B,8OAAC;;0EACC,8OAAC;0EAAO;;;;;;4DAAoB;;;;;;;kEAG9B,8OAAC;;0EACC,8OAAC;0EAAO;;;;;;4DAAoB;;;;;;;kEAI9B,8OAAC;;0EACC,8OAAC;0EAAO;;;;;;4DAA0B;;;;;;;;;;;;;;;;;;;kDAKxC,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOP,8OAAC,2HAAA,CAAA,eAAY;sCACX,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAU;sCAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtE;uCAEe", "debugId": null}}, {"offset": {"line": 4514, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/app/%28root%29/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Footer from \"@/sections/Footer\";\r\nimport Header from \"@/sections/Header\";\r\nimport React, { ReactNode, useEffect } from \"react\";\r\nimport { CartProvider } from \"@/contexts/CartContext\";\r\nimport { OrderProvider } from \"@/contexts/OrderContext\";\r\nimport { WishlistProvider } from \"@/contexts/WishlistContext\";\r\nimport { ModalProvider } from \"@/contexts/ModalContext\";\r\nimport { TermsAcceptanceProvider } from \"@/contexts/TermsAcceptanceContext\";\r\nimport WhatsAppButton from \"@/components/WhatsAppButton\";\r\nimport BackToTopButton from \"@/components/BackToTopButton\";\r\nimport TermsAcceptanceModal from \"@/components/TermsAcceptanceModal\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  isGoogleAuthInProgress,\r\n  clearGoogleAuthInProgress,\r\n} from \"@/lib/auth/authUtils\";\r\n\r\nconst Layout = ({ children }: { children: ReactNode }) => {\r\n  const pathname = usePathname();\r\n  const isHomePage = pathname === \"/\";\r\n\r\n  // Check for Google auth in progress on client side\r\n  useEffect(() => {\r\n    // If Google auth is in progress and we're on the home page,\r\n    // this could indicate a redirect loop\r\n    if (isGoogleAuthInProgress() && window.location.pathname === \"/\") {\r\n      console.warn(\r\n        \"Detected potential Google auth redirect loop, clearing flag\"\r\n      );\r\n      // Clear the flag to break the loop using our utility function\r\n      clearGoogleAuthInProgress();\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <CartProvider>\r\n      <OrderProvider>\r\n        <WishlistProvider>\r\n          <ModalProvider>\r\n            <TermsAcceptanceProvider>\r\n              <div>\r\n                {/* Only render Header if not on home page (Hero has integrated header) */}\r\n                {!isHomePage && <Header />}\r\n                <div className=\"relative\">{children}</div>\r\n                <Footer />\r\n                <WhatsAppButton phoneNumber=\"03421401866\" />\r\n                <BackToTopButton />\r\n                <TermsAcceptanceModal />\r\n              </div>\r\n            </TermsAcceptanceProvider>\r\n          </ModalProvider>\r\n        </WishlistProvider>\r\n      </OrderProvider>\r\n    </CartProvider>\r\n  );\r\n};\r\n\r\nexport default Layout;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;;AAmBA,MAAM,SAAS,CAAC,EAAE,QAAQ,EAA2B;IACnD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,aAAa,aAAa;IAEhC,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4DAA4D;QAC5D,sCAAsC;QACtC,IAAI,CAAA,GAAA,wHAAA,CAAA,yBAAsB,AAAD,OAAO,OAAO,QAAQ,CAAC,QAAQ,KAAK,KAAK;YAChE,QAAQ,IAAI,CACV;YAEF,8DAA8D;YAC9D,CAAA,GAAA,wHAAA,CAAA,4BAAyB,AAAD;QAC1B;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,wHAAA,CAAA,eAAY;kBACX,cAAA,8OAAC,yHAAA,CAAA,gBAAa;sBACZ,cAAA,8OAAC,4HAAA,CAAA,mBAAgB;0BACf,cAAA,8OAAC,yHAAA,CAAA,gBAAa;8BACZ,cAAA,8OAAC,mIAAA,CAAA,0BAAuB;kCACtB,cAAA,8OAAC;;gCAEE,CAAC,4BAAc,8OAAC,mHAAA,CAAA,UAAM;;;;;8CACvB,8OAAC;oCAAI,WAAU;8CAAY;;;;;;8CAC3B,8OAAC,mHAAA,CAAA,UAAM;;;;;8CACP,8OAAC,6HAAA,CAAA,UAAc;oCAAC,aAAY;;;;;;8CAC5B,8OAAC,8HAAA,CAAA,UAAe;;;;;8CAChB,8OAAC,mIAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;uCAEe", "debugId": null}}]}