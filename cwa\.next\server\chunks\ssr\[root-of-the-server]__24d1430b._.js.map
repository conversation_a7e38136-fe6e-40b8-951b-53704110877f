{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_96f51991.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_96f51991-module__OF3C0q__className\",\n  \"variable\": \"poppins_96f51991-module__OF3C0q__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_96f51991.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22],%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22fallback%22:[%22system-ui%22,%22Arial%22,%22sans-serif%22],%22variable%22:%22--font-poppins%22}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', system-ui, Arial, sans-serif\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/contexts/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AuthContext.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AuthContext.tsx <module evaluation>\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/contexts/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AuthContext.tsx\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/AuthContext.tsx\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sCACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,sCACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/contexts/CurrencyContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CurrencyProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CurrencyProvider() from the server but CurrencyProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/CurrencyContext.tsx <module evaluation>\",\n    \"CurrencyProvider\",\n);\nexport const useCurrency = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCurrency() from the server but useCurrency is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/CurrencyContext.tsx <module evaluation>\",\n    \"useCurrency\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8DACA", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/contexts/CurrencyContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CurrencyProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CurrencyProvider() from the server but CurrencyProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/CurrencyContext.tsx\",\n    \"CurrencyProvider\",\n);\nexport const useCurrency = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCurrency() from the server but useCurrency is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/contexts/CurrencyContext.tsx\",\n    \"useCurrency\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0CACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0CACA", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/config/site.ts"], "sourcesContent": ["/**\r\n * Site configuration file\r\n * \r\n * This file contains all the site-wide configuration settings.\r\n * Update these values to match your company information.\r\n */\r\n\r\nexport const siteConfig = {\r\n  // Company Information\r\n  name: \"Chinioti Wooden Art\",\r\n  shortName: \"Chinioti Art\",\r\n  description: \"Pakistan's premier destination for authentic Chinioti wooden furniture, handcrafted with traditional craftsmanship and modern designs.\",\r\n  slogan: \"Traditional Craftsmanship, Modern Designs\",\r\n  \r\n  // Contact Information\r\n  contact: {\r\n    email: \"<EMAIL>\",\r\n    phone: \"+923421401866\",\r\n    whatsapp: \"+923421401866\",\r\n    address: {\r\n      street: \"Chiniot Furniture Market\",\r\n      city: \"Chiniot\",\r\n      region: \"Punjab\",\r\n      postalCode: \"35400\",\r\n      country: \"Pakistan\",\r\n    },\r\n  },\r\n  \r\n  // Social Media\r\n  social: {\r\n    facebook: \"https://www.facebook.com/chiniotiart\",\r\n    instagram: \"https://www.instagram.com/chiniotiart\",\r\n    twitter: \"https://twitter.com/chiniotiart\",\r\n    youtube: \"https://www.youtube.com/chiniotiart\",\r\n  },\r\n  \r\n  // SEO Configuration\r\n  seo: {\r\n    // Domain name (without https:// or trailing slash)\r\n    domain: \"chiniotiwoodenart.com\",\r\n\r\n    // Default title template\r\n    titleTemplate: \"%s | Chinioti Wooden Art - Premium Furniture from Chiniot\",\r\n\r\n    // Comprehensive keywords for Chiniot furniture SEO\r\n    keywords: [\r\n      // Primary Chiniot furniture keywords\r\n      \"Chinioti furniture\",\r\n      \"Chiniot furniture\",\r\n      \"Chiniot wooden furniture\",\r\n      \"Chinioti wooden art\",\r\n      \"Chiniot furniture Pakistan\",\r\n      \"authentic Chiniot furniture\",\r\n      \"traditional Chiniot furniture\",\r\n      \"handcrafted Chiniot furniture\",\r\n\r\n      // Product-specific keywords\r\n      \"wooden beds Chiniot\",\r\n      \"wooden tables Chiniot\",\r\n      \"wooden chairs Chiniot\",\r\n      \"wooden wardrobes Chiniot\",\r\n      \"wooden dining sets Chiniot\",\r\n      \"wooden sofa sets Chiniot\",\r\n      \"wooden cabinets Chiniot\",\r\n      \"wooden dressing tables Chiniot\",\r\n\r\n      // Local and regional keywords\r\n      \"furniture Chiniot Punjab\",\r\n      \"Pakistan wooden furniture\",\r\n      \"Punjab furniture makers\",\r\n      \"Chiniot craftsmen\",\r\n      \"Chiniot artisans\",\r\n      \"furniture from Chiniot\",\r\n      \"Chiniot furniture market\",\r\n      \"Chiniot furniture industry\",\r\n\r\n      // Quality and craftsmanship keywords\r\n      \"handcrafted wooden furniture\",\r\n      \"premium wooden furniture\",\r\n      \"luxury wooden furniture\",\r\n      \"custom wooden furniture\",\r\n      \"solid wood furniture\",\r\n      \"carved wooden furniture\",\r\n      \"traditional craftsmanship\",\r\n      \"artisan furniture\",\r\n\r\n      // Business keywords\r\n      \"furniture manufacturer Pakistan\",\r\n      \"wooden furniture supplier\",\r\n      \"furniture export Pakistan\",\r\n      \"wholesale furniture Chiniot\",\r\n      \"furniture online Pakistan\",\r\n      \"buy Chiniot furniture\",\r\n      \"Chiniot furniture price\",\r\n      \"furniture delivery Pakistan\"\r\n    ],\r\n    \r\n    // Location coordinates for maps\r\n    location: {\r\n      latitude: 31.7094062,\r\n      longitude: 72.9703328,\r\n    },\r\n    \r\n    // Business hours\r\n    businessHours: {\r\n      weekdays: {\r\n        open: \"09:00\",\r\n        close: \"18:00\",\r\n      },\r\n      saturday: {\r\n        open: \"09:00\",\r\n        close: \"18:00\",\r\n      },\r\n      sunday: {\r\n        open: \"10:00\",\r\n        close: \"16:00\",\r\n      },\r\n    },\r\n\r\n    // Additional SEO metadata\r\n    organization: {\r\n      type: \"FurnitureStore\",\r\n      foundingDate: \"2010\",\r\n      priceRange: \"$$-$$$\",\r\n      paymentAccepted: [\"Cash\", \"Credit Card\", \"Bank Transfer\", \"Mobile Payment\"],\r\n      currenciesAccepted: [\"PKR\", \"USD\"],\r\n      areaServed: [\r\n        \"Pakistan\",\r\n        \"Punjab\",\r\n        \"Chiniot\",\r\n        \"Faisalabad\",\r\n        \"Lahore\",\r\n        \"Islamabad\",\r\n        \"Karachi\"\r\n      ],\r\n      serviceArea: {\r\n        type: \"Country\",\r\n        name: \"Pakistan\"\r\n      }\r\n    },\r\n\r\n    // Local SEO data\r\n    localSEO: {\r\n      region: \"Punjab\",\r\n      locality: \"Chiniot\",\r\n      nearbyLandmarks: [\r\n        \"Chiniot Furniture Market\",\r\n        \"Chiniot Railway Station\",\r\n        \"River Chenab\",\r\n        \"Chiniot Bridge\"\r\n      ],\r\n      servingAreas: [\r\n        \"Chiniot District\",\r\n        \"Faisalabad Division\",\r\n        \"Punjab Province\",\r\n        \"Pakistan\"\r\n      ]\r\n    },\r\n\r\n    // Rich snippets data\r\n    richSnippets: {\r\n      aggregateRating: {\r\n        ratingValue: \"4.8\",\r\n        reviewCount: \"150\",\r\n        bestRating: \"5\",\r\n        worstRating: \"1\"\r\n      },\r\n      offers: {\r\n        availability: \"InStock\",\r\n        priceCurrency: \"PKR\",\r\n        validFrom: new Date().toISOString(),\r\n        validThrough: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()\r\n      }\r\n    }\r\n  },\r\n  \r\n  // Theme Configuration\r\n  theme: {\r\n    // Primary colors\r\n    colors: {\r\n      primary: \"#8B4513\", // Brown\r\n      secondary: \"#D2B48C\", // Tan\r\n      accent: \"#A0522D\", // Sienna\r\n      background: \"#F8F8F8\", // Off-white\r\n      text: \"#333333\", // Dark gray\r\n    },\r\n    \r\n    // Font families\r\n    fonts: {\r\n      primary: \"Poppins\",\r\n      secondary: \"serif\",\r\n    },\r\n  },\r\n};\r\n\r\n// Helper function to get the full URL\r\nexport const getUrl = (path: string = \"\") => {\r\n  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || `https://${siteConfig.seo.domain}`;\r\n  return `${baseUrl}${path.startsWith(\"/\") ? path : `/${path}`}`;\r\n};\r\n\r\nexport default siteConfig;\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAEM,MAAM,aAAa;IACxB,sBAAsB;IACtB,MAAM;IACN,WAAW;IACX,aAAa;IACb,QAAQ;IAER,sBAAsB;IACtB,SAAS;QACP,OAAO;QACP,OAAO;QACP,UAAU;QACV,SAAS;YACP,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,YAAY;YACZ,SAAS;QACX;IACF;IAEA,eAAe;IACf,QAAQ;QACN,UAAU;QACV,WAAW;QACX,SAAS;QACT,SAAS;IACX;IAEA,oBAAoB;IACpB,KAAK;QACH,mDAAmD;QACnD,QAAQ;QAER,yBAAyB;QACzB,eAAe;QAEf,mDAAmD;QACnD,UAAU;YACR,qCAAqC;YACrC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,4BAA4B;YAC5B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,8BAA8B;YAC9B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,qCAAqC;YACrC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,oBAAoB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,gCAAgC;QAChC,UAAU;YACR,UAAU;YACV,WAAW;QACb;QAEA,iBAAiB;QACjB,eAAe;YACb,UAAU;gBACR,MAAM;gBACN,OAAO;YACT;YACA,UAAU;gBACR,MAAM;gBACN,OAAO;YACT;YACA,QAAQ;gBACN,MAAM;gBACN,OAAO;YACT;QACF;QAEA,0BAA0B;QAC1B,cAAc;YACZ,MAAM;YACN,cAAc;YACd,YAAY;YACZ,iBAAiB;gBAAC;gBAAQ;gBAAe;gBAAiB;aAAiB;YAC3E,oBAAoB;gBAAC;gBAAO;aAAM;YAClC,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,aAAa;gBACX,MAAM;gBACN,MAAM;YACR;QACF;QAEA,iBAAiB;QACjB,UAAU;YACR,QAAQ;YACR,UAAU;YACV,iBAAiB;gBACf;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;QACH;QAEA,qBAAqB;QACrB,cAAc;YACZ,iBAAiB;gBACf,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,aAAa;YACf;YACA,QAAQ;gBACN,cAAc;gBACd,eAAe;gBACf,WAAW,IAAI,OAAO,WAAW;gBACjC,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5E;QACF;IACF;IAEA,sBAAsB;IACtB,OAAO;QACL,iBAAiB;QACjB,QAAQ;YACN,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,MAAM;QACR;QAEA,gBAAgB;QAChB,OAAO;YACL,SAAS;YACT,WAAW;QACb;IACF;AACF;AAGO,MAAM,SAAS,CAAC,OAAe,EAAE;IACtC,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI,CAAC,QAAQ,EAAE,WAAW,GAAG,CAAC,MAAM,EAAE;IACtF,OAAO,GAAG,UAAU,KAAK,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;AAChE;uCAEe", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/SEO/EnhancedSEO.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/SEO/EnhancedSEO.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/SEO/EnhancedSEO.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/components/SEO/EnhancedSEO.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/SEO/EnhancedSEO.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/SEO/EnhancedSEO.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON>, Viewport } from \"next\";\r\nimport { Poppins } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport { Toaster } from \"sonner\";\r\nimport { AuthProvider } from \"@/contexts/AuthContext\";\r\nimport { CurrencyProvider } from \"@/contexts/CurrencyContext\";\r\nimport { siteConfig, getUrl } from \"@/config/site\";\r\nimport EnhancedSEO from \"@/components/SEO/EnhancedSEO\";\r\n\r\nconst poppins = Poppins({\r\n  weight: [\"400\", \"500\", \"600\", \"700\"],\r\n  subsets: [\"latin\"],\r\n  display: \"swap\",\r\n  fallback: [\"system-ui\", \"Arial\", \"sans-serif\"],\r\n  variable: \"--font-poppins\",\r\n});\r\n\r\n// Define viewport metadata\r\nexport const viewport: Viewport = {\r\n  width: \"device-width\",\r\n  initialScale: 1,\r\n  maximumScale: 5,\r\n  themeColor: siteConfig.theme.colors.primary,\r\n};\r\n\r\n// Enhanced metadata for the site with comprehensive SEO\r\nexport const metadata: Metadata = {\r\n  metadataBase: new URL(getUrl()),\r\n  title: {\r\n    default: `${siteConfig.name} - Premium Handcrafted Furniture from Chiniot, Pakistan`,\r\n    template: `%s | ${siteConfig.name} - Authentic Chiniot Furniture`,\r\n  },\r\n  description: `${siteConfig.description} Discover authentic handcrafted wooden furniture made by skilled artisans in Chiniot, Punjab, Pakistan. Premium quality beds, tables, chairs, wardrobes and more.`,\r\n  keywords: siteConfig.seo.keywords,\r\n  authors: [{ name: siteConfig.name, url: getUrl() }],\r\n  creator: siteConfig.name,\r\n  publisher: siteConfig.name,\r\n\r\n  // Enhanced format detection\r\n  formatDetection: {\r\n    email: true,\r\n    address: true,\r\n    telephone: true,\r\n  },\r\n\r\n  // Comprehensive robots configuration\r\n  robots: {\r\n    index: true,\r\n    follow: true,\r\n    googleBot: {\r\n      index: true,\r\n      follow: true,\r\n      \"max-image-preview\": \"large\",\r\n      \"max-video-preview\": -1,\r\n      \"max-snippet\": -1,\r\n    },\r\n  },\r\n\r\n  // Enhanced Open Graph with local business context\r\n  openGraph: {\r\n    type: \"website\",\r\n    locale: \"en_US\",\r\n    url: getUrl(),\r\n    siteName: siteConfig.name,\r\n    title: `${siteConfig.name} - Premium Handcrafted Furniture from Chiniot, Pakistan`,\r\n    description: `${siteConfig.description} Discover authentic handcrafted wooden furniture made by skilled artisans in Chiniot, Punjab, Pakistan.`,\r\n    images: [\r\n      {\r\n        url: getUrl(\"/og-image.jpg\"),\r\n        width: 1200,\r\n        height: 630,\r\n        alt: `${siteConfig.name} - Premium Chiniot Furniture from Pakistan`,\r\n      },\r\n      {\r\n        url: getUrl(\"/og-image.svg\"),\r\n        width: 1200,\r\n        height: 630,\r\n        alt: `${siteConfig.name} - Handcrafted Wooden Furniture`,\r\n      },\r\n    ],\r\n  },\r\n\r\n  // Enhanced Twitter Card\r\n  twitter: {\r\n    card: \"summary_large_image\",\r\n    title: `${siteConfig.name} - Premium Chiniot Furniture from Pakistan`,\r\n    description: `${siteConfig.description} Authentic handcrafted wooden furniture from Chiniot artisans.`,\r\n    images: [getUrl(\"/twitter-image.jpg\")],\r\n    creator: \"@chiniotiart\",\r\n    site: \"@chiniotiart\",\r\n  },\r\n\r\n  // Comprehensive icons and app metadata\r\n  icons: {\r\n    icon: [\r\n      { url: \"/favicon.ico\", sizes: \"any\" },\r\n      { url: \"/favicon.svg\", type: \"image/svg+xml\" },\r\n    ],\r\n    apple: [\r\n      { url: \"/apple-touch-icon.png\", sizes: \"180x180\", type: \"image/png\" },\r\n    ],\r\n    other: [\r\n      {\r\n        rel: \"mask-icon\",\r\n        url: \"/safari-pinned-tab.svg\",\r\n        color: siteConfig.theme.colors.primary,\r\n      },\r\n    ],\r\n  },\r\n\r\n  // PWA manifest\r\n  manifest: \"/manifest.json\",\r\n\r\n  // Enhanced alternates with hreflang\r\n  alternates: {\r\n    canonical: getUrl(),\r\n    languages: {\r\n      \"en-US\": getUrl(),\r\n      \"en\": getUrl(),\r\n    },\r\n  },\r\n\r\n  // App-specific metadata\r\n  applicationName: siteConfig.name,\r\n  referrer: \"origin-when-cross-origin\",\r\n  colorScheme: \"light\",\r\n\r\n  // Verification tags (add your actual verification codes in environment variables)\r\n  verification: {\r\n    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION || \"\",\r\n    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION || \"\",\r\n    yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION || \"\",\r\n    other: {\r\n      \"msvalidate.01\": process.env.NEXT_PUBLIC_BING_VERIFICATION || \"\",\r\n      \"facebook-domain-verification\": process.env.NEXT_PUBLIC_FACEBOOK_VERIFICATION || \"\",\r\n    },\r\n  },\r\n\r\n  // Additional metadata for local SEO\r\n  other: {\r\n    \"geo.region\": \"PK-PB\",\r\n    \"geo.placename\": \"Chiniot, Punjab, Pakistan\",\r\n    \"geo.position\": `${siteConfig.seo.location.latitude};${siteConfig.seo.location.longitude}`,\r\n    \"ICBM\": `${siteConfig.seo.location.latitude}, ${siteConfig.seo.location.longitude}`,\r\n    \"business:contact_data:street_address\": siteConfig.contact.address.street,\r\n    \"business:contact_data:locality\": siteConfig.contact.address.city,\r\n    \"business:contact_data:region\": siteConfig.contact.address.region,\r\n    \"business:contact_data:postal_code\": siteConfig.contact.address.postalCode,\r\n    \"business:contact_data:country_name\": siteConfig.contact.address.country,\r\n    \"business:contact_data:phone_number\": siteConfig.contact.phone,\r\n    \"business:contact_data:email\": siteConfig.contact.email,\r\n  },\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\" className={poppins.className}>\r\n      <head>\r\n        {/* Enhanced SEO Components */}\r\n        <EnhancedSEO />\r\n\r\n        {/* Favicon and App Icons */}\r\n        <link rel=\"icon\" href=\"/favicon.ico\" sizes=\"any\" />\r\n        <link rel=\"icon\" href=\"/favicon.svg\" type=\"image/svg+xml\" />\r\n        <link rel=\"apple-touch-icon\" href=\"/apple-touch-icon.png\" />\r\n        <link rel=\"manifest\" href=\"/manifest.json\" />\r\n\r\n        {/* Preconnect to external domains for performance */}\r\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\r\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\r\n        <link rel=\"preconnect\" href=\"https://www.google-analytics.com\" />\r\n        <link rel=\"preconnect\" href=\"https://www.googletagmanager.com\" />\r\n\r\n        {/* DNS prefetch for better performance */}\r\n        <link rel=\"dns-prefetch\" href=\"//fonts.googleapis.com\" />\r\n        <link rel=\"dns-prefetch\" href=\"//fonts.gstatic.com\" />\r\n        <link rel=\"dns-prefetch\" href=\"//www.google-analytics.com\" />\r\n\r\n        {/* Additional meta tags for mobile optimization */}\r\n        <meta name=\"mobile-web-app-capable\" content=\"yes\" />\r\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\r\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\r\n        <meta name=\"apple-mobile-web-app-title\" content={siteConfig.shortName} />\r\n        <meta name=\"msapplication-TileColor\" content={siteConfig.theme.colors.primary} />\r\n        <meta name=\"theme-color\" content={siteConfig.theme.colors.primary} />\r\n      </head>\r\n      <body className=\"uppercase antialiased bg-background text-foreground font-sans\">\r\n        <AuthProvider>\r\n          <CurrencyProvider>\r\n            {children}\r\n            <Toaster\r\n              position=\"top-right\"\r\n              richColors\r\n              closeButton\r\n              expand={false}\r\n              visibleToasts={5}\r\n              duration={4000}\r\n              toastOptions={{\r\n                style: {\r\n                  marginTop: '30px',\r\n                },\r\n              }}\r\n            />\r\n          </CurrencyProvider>\r\n        </AuthProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;AAWO,MAAM,WAAqB;IAChC,OAAO;IACP,cAAc;IACd,cAAc;IACd,YAAY,8GAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;AAC7C;AAGO,MAAM,WAAqB;IAChC,cAAc,IAAI,IAAI,CAAA,GAAA,8GAAA,CAAA,SAAM,AAAD;IAC3B,OAAO;QACL,SAAS,GAAG,8GAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uDAAuD,CAAC;QACpF,UAAU,CAAC,KAAK,EAAE,8GAAA,CAAA,aAAU,CAAC,IAAI,CAAC,8BAA8B,CAAC;IACnE;IACA,aAAa,GAAG,8GAAA,CAAA,aAAU,CAAC,WAAW,CAAC,iKAAiK,CAAC;IACzM,UAAU,8GAAA,CAAA,aAAU,CAAC,GAAG,CAAC,QAAQ;IACjC,SAAS;QAAC;YAAE,MAAM,8GAAA,CAAA,aAAU,CAAC,IAAI;YAAE,KAAK,CAAA,GAAA,8GAAA,CAAA,SAAM,AAAD;QAAI;KAAE;IACnD,SAAS,8GAAA,CAAA,aAAU,CAAC,IAAI;IACxB,WAAW,8GAAA,CAAA,aAAU,CAAC,IAAI;IAE1B,4BAA4B;IAC5B,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IAEA,qCAAqC;IACrC,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB;YACrB,qBAAqB,CAAC;YACtB,eAAe,CAAC;QAClB;IACF;IAEA,kDAAkD;IAClD,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,CAAA,GAAA,8GAAA,CAAA,SAAM,AAAD;QACV,UAAU,8GAAA,CAAA,aAAU,CAAC,IAAI;QACzB,OAAO,GAAG,8GAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uDAAuD,CAAC;QAClF,aAAa,GAAG,8GAAA,CAAA,aAAU,CAAC,WAAW,CAAC,uGAAuG,CAAC;QAC/I,QAAQ;YACN;gBACE,KAAK,CAAA,GAAA,8GAAA,CAAA,SAAM,AAAD,EAAE;gBACZ,OAAO;gBACP,QAAQ;gBACR,KAAK,GAAG,8GAAA,CAAA,aAAU,CAAC,IAAI,CAAC,0CAA0C,CAAC;YACrE;YACA;gBACE,KAAK,CAAA,GAAA,8GAAA,CAAA,SAAM,AAAD,EAAE;gBACZ,OAAO;gBACP,QAAQ;gBACR,KAAK,GAAG,8GAAA,CAAA,aAAU,CAAC,IAAI,CAAC,+BAA+B,CAAC;YAC1D;SACD;IACH;IAEA,wBAAwB;IACxB,SAAS;QACP,MAAM;QACN,OAAO,GAAG,8GAAA,CAAA,aAAU,CAAC,IAAI,CAAC,0CAA0C,CAAC;QACrE,aAAa,GAAG,8GAAA,CAAA,aAAU,CAAC,WAAW,CAAC,8DAA8D,CAAC;QACtG,QAAQ;YAAC,CAAA,GAAA,8GAAA,CAAA,SAAM,AAAD,EAAE;SAAsB;QACtC,SAAS;QACT,MAAM;IACR;IAEA,uCAAuC;IACvC,OAAO;QACL,MAAM;YACJ;gBAAE,KAAK;gBAAgB,OAAO;YAAM;YACpC;gBAAE,KAAK;gBAAgB,MAAM;YAAgB;SAC9C;QACD,OAAO;YACL;gBAAE,KAAK;gBAAyB,OAAO;gBAAW,MAAM;YAAY;SACrE;QACD,OAAO;YACL;gBACE,KAAK;gBACL,KAAK;gBACL,OAAO,8GAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;YACxC;SACD;IACH;IAEA,eAAe;IACf,UAAU;IAEV,oCAAoC;IACpC,YAAY;QACV,WAAW,CAAA,GAAA,8GAAA,CAAA,SAAM,AAAD;QAChB,WAAW;YACT,SAAS,CAAA,GAAA,8GAAA,CAAA,SAAM,AAAD;YACd,MAAM,CAAA,GAAA,8GAAA,CAAA,SAAM,AAAD;QACb;IACF;IAEA,wBAAwB;IACxB,iBAAiB,8GAAA,CAAA,aAAU,CAAC,IAAI;IAChC,UAAU;IACV,aAAa;IAEb,kFAAkF;IAClF,cAAc;QACZ,QAAQ,QAAQ,GAAG,CAAC,+BAA+B,IAAI;QACvD,QAAQ,QAAQ,GAAG,CAAC,+BAA+B,IAAI;QACvD,OAAO,QAAQ,GAAG,CAAC,8BAA8B,IAAI;QACrD,OAAO;YACL,iBAAiB,QAAQ,GAAG,CAAC,6BAA6B,IAAI;YAC9D,gCAAgC,QAAQ,GAAG,CAAC,iCAAiC,IAAI;QACnF;IACF;IAEA,oCAAoC;IACpC,OAAO;QACL,cAAc;QACd,iBAAiB;QACjB,gBAAgB,GAAG,8GAAA,CAAA,aAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,8GAAA,CAAA,aAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE;QAC1F,QAAQ,GAAG,8GAAA,CAAA,aAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,8GAAA,CAAA,aAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE;QACnF,wCAAwC,8GAAA,CAAA,aAAU,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM;QACzE,kCAAkC,8GAAA,CAAA,aAAU,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI;QACjE,gCAAgC,8GAAA,CAAA,aAAU,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM;QACjE,qCAAqC,8GAAA,CAAA,aAAU,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU;QAC1E,sCAAsC,8GAAA,CAAA,aAAU,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO;QACxE,sCAAsC,8GAAA,CAAA,aAAU,CAAC,OAAO,CAAC,KAAK;QAC9D,+BAA+B,8GAAA,CAAA,aAAU,CAAC,OAAO,CAAC,KAAK;IACzD;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,2IAAA,CAAA,UAAO,CAAC,SAAS;;0BAC1C,8OAAC;;kCAEC,8OAAC,iIAAA,CAAA,UAAW;;;;;kCAGZ,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAe,OAAM;;;;;;kCAC3C,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAe,MAAK;;;;;;kCAC1C,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBAAK,KAAI;wBAAW,MAAK;;;;;;kCAG1B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAG5B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAG9B,8OAAC;wBAAK,MAAK;wBAAyB,SAAQ;;;;;;kCAC5C,8OAAC;wBAAK,MAAK;wBAA+B,SAAQ;;;;;;kCAClD,8OAAC;wBAAK,MAAK;wBAAwC,SAAQ;;;;;;kCAC3D,8OAAC;wBAAK,MAAK;wBAA6B,SAAS,8GAAA,CAAA,aAAU,CAAC,SAAS;;;;;;kCACrE,8OAAC;wBAAK,MAAK;wBAA0B,SAAS,8GAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;;;;;;kCAC7E,8OAAC;wBAAK,MAAK;wBAAc,SAAS,8GAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;;;;;;;;;;;;0BAEnE,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,wHAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,4HAAA,CAAA,mBAAgB;;4BACd;0CACD,8OAAC,wIAAA,CAAA,UAAO;gCACN,UAAS;gCACT,UAAU;gCACV,WAAW;gCACX,QAAQ;gCACR,eAAe;gCACf,UAAU;gCACV,cAAc;oCACZ,OAAO;wCACL,WAAW;oCACb;gCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/sonner/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"toast\",\n);\nexport const useSonner = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"useSonner\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,oEACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,oEACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Softwares/web-lab/cwa/node_modules/sonner/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"toast\",\n);\nexport const useSonner = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"useSonner\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,gDACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,gDACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}